{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue?vue&type=template&id=795f54f0&scoped=true", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue", "mtime": 1749362335849}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "ref", "_createBlock", "_component_el_dialog", "$options", "visible", "$event", "title", "$props", "patientName", "width", "handleClose", "top", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "src", "getPatientAvatarUrl", "alt", "onError", "_cache", "args", "handleHeaderImageError", "_hoisted_5", "_hoisted_6", "_toDisplayString", "_hoisted_7", "_createVNode", "_component_el_button", "size", "type", "onClick", "loadMessages", "_hoisted_8", "_hoisted_9", "$data", "messages", "length", "_createElementBlock", "_hoisted_10", "_Fragment", "_renderList", "message", "index", "key", "_normalizeClass", "flag", "_hoisted_11", "getAvatarUrl", "handleImageError", "_hoisted_13", "_hoisted_14", "_hoisted_15", "content", "_hoisted_16", "formatTime", "sendtime", "_hoisted_17", "_hoisted_18", "_component_el_input", "inputMessage", "rows", "placeholder", "maxlength", "onKeyup", "_with<PERSON><PERSON><PERSON>", "_withModifiers", "sendMessage", "_hoisted_19", "_hoisted_20", "loading", "sending", "disabled", "trim"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    v-model=\"visible\"\n    :title=\"`与${patientName}的对话`\"\n    width=\"700px\"\n    :before-close=\"handleClose\"\n    class=\"chat-dialog\"\n    top=\"5vh\"\n  >\n    <!-- 聊天头部信息 -->\n    <div class=\"chat-header\">\n      <div class=\"patient-info\">\n        <div class=\"avatar\">\n          <img\n            :src=\"getPatientAvatarUrl()\"\n            :alt=\"patientName\"\n            @error=\"handleHeaderImageError\"\n          />\n        </div>\n        <div class=\"info\">\n          <div class=\"name\">{{ patientName }}</div>\n          <div class=\"status\">在线咨询</div>\n        </div>\n      </div>\n      <div class=\"chat-actions\">\n        <el-button size=\"small\" type=\"text\" @click=\"loadMessages\">\n          <i class=\"el-icon-refresh\"></i> 刷新\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 聊天消息区域 -->\n    <div class=\"chat-container\" ref=\"chatContainer\">\n      <div class=\"chat-messages\">\n        <div v-if=\"messages.length === 0\" class=\"empty-messages\">\n          <i class=\"el-icon-chat-dot-round\"></i>\n          <p>暂无聊天记录</p>\n        </div>\n        <div\n          v-for=\"(message, index) in messages\"\n          :key=\"index\"\n          :class=\"['message-item', message.flag == '2' ? 'doctor-message' : 'patient-message']\"\n        >\n          <div class=\"avatar-wrapper\">\n            <div class=\"message-avatar\" :class=\"message.flag == '2' ? 'doctor-avatar' : 'patient-avatar'\">\n              <img\n                :src=\"getAvatarUrl(message)\"\n                :alt=\"message.flag == '2' ? '医生' : '患者'\"\n                @error=\"handleImageError\"\n              />\n            </div>\n          </div>\n          <div class=\"message-wrapper\">\n            <div class=\"message-content\">\n              <div class=\"message-text\">{{ message.content }}</div>\n            </div>\n            <div class=\"message-time\">{{ formatTime(message.sendtime) }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 输入区域 -->\n    <div class=\"chat-input\">\n      <div class=\"input-wrapper\">\n        <el-input\n          v-model=\"inputMessage\"\n          type=\"textarea\"\n          :rows=\"3\"\n          placeholder=\"请输入回复内容...\"\n          maxlength=\"500\"\n          show-word-limit\n          @keyup.ctrl.enter=\"sendMessage\"\n          class=\"message-input\"\n        ></el-input>\n        <div class=\"input-actions\">\n          <div class=\"input-tips\">\n            <span class=\"tip-item\">\n              <i class=\"el-icon-info\"></i>\n              Ctrl + Enter 快速发送\n            </span>\n            <span class=\"tip-item\">\n              <i class=\"el-icon-time\"></i>\n              每5秒自动刷新\n            </span>\n          </div>\n          <div class=\"send-actions\">\n            <el-button @click=\"inputMessage = ''\" size=\"small\">清空</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"sendMessage\"\n              :loading=\"sending\"\n              :disabled=\"!inputMessage.trim()\"\n              class=\"send-btn\"\n            >\n              <i class=\"el-icon-s-promotion\"></i>\n              发送\n            </el-button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport request, { base } from \"../../utils/http\";\n\nexport default {\n  name: 'ChatDialog',\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    patientName: {\n      type: String,\n      default: ''\n    },\n    patientId: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['update:modelValue', 'message-sent'],\n  data() {\n    return {\n      messages: [],\n      inputMessage: '',\n      sending: false,\n      refreshTimer: null,\n      fileBasePath: 'http://127.0.0.1:8088/OnlineMedicalAppointmentSystem_Server/upload/'\n    };\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.modelValue;\n      },\n      set(value) {\n        this.$emit('update:modelValue', value);\n      }\n    }\n  },\n  watch: {\n    visible(newVal) {\n      if (newVal) {\n        this.loadMessages();\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    }\n  },\n  methods: {\n    // 加载聊天消息\n    loadMessages() {\n      if (!this.patientId) return;\n\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      const param = {\n        lname: this.patientName,\n        did: user.did\n      };\n\n      const url = base + \"/chatinfo/list2?currentPage=1&pageSize=5000\";\n      request.post(url, param).then((res) => {\n        if (res.resdata) {\n          this.messages = res.resdata.sort((a, b) => {\n            return new Date(a.sendtime) - new Date(b.sendtime);\n          });\n          this.$nextTick(() => {\n            this.scrollToBottom();\n          });\n        }\n      }).catch((error) => {\n        console.error('加载聊天消息失败:', error);\n        this.$message.error('加载聊天消息失败');\n      });\n    },\n\n    // 发送消息\n    sendMessage() {\n      if (!this.inputMessage.trim()) {\n        this.$message.warning('请输入消息内容');\n        return;\n      }\n\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      this.sending = true;\n\n      const param = {\n        lname: this.patientName,\n        did: user.did,\n        content: this.inputMessage,\n        flag: \"2\", // 医生发送的消息\n        sendtime: new Date().toLocaleString()\n      };\n\n      const url = base + \"/chatinfo/add\";\n      request.post(url, param).then((res) => {\n        this.sending = false;\n        this.inputMessage = '';\n        this.$message.success('发送成功');\n\n        // 立即刷新消息列表\n        setTimeout(() => {\n          this.loadMessages();\n        }, 500);\n\n        // 通知父组件消息已发送\n        this.$emit('message-sent');\n      }).catch((error) => {\n        this.sending = false;\n        console.error('发送消息失败:', error);\n        this.$message.error('发送消息失败');\n      });\n    },\n\n    // 滚动到底部\n    scrollToBottom() {\n      const container = this.$refs.chatContainer;\n      if (container) {\n        container.scrollTop = container.scrollHeight;\n      }\n    },\n\n    // 开始自动刷新\n    startAutoRefresh() {\n      this.stopAutoRefresh(); // 先清除之前的定时器\n      this.refreshTimer = setInterval(() => {\n        this.loadMessages();\n      }, 5000); // 每5秒刷新一次\n    },\n\n    // 停止自动刷新\n    stopAutoRefresh() {\n      if (this.refreshTimer) {\n        clearInterval(this.refreshTimer);\n        this.refreshTimer = null;\n      }\n    },\n\n    // 格式化时间\n    formatTime(timeStr) {\n      if (!timeStr) return '';\n      try {\n        const date = new Date(timeStr);\n        const now = new Date();\n        const diff = now - date;\n\n        // 如果是今天\n        if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {\n          return date.toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit'\n          });\n        }\n\n        // 如果是昨天\n        const yesterday = new Date(now);\n        yesterday.setDate(now.getDate() - 1);\n        if (date.getDate() === yesterday.getDate()) {\n          return '昨天 ' + date.toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit'\n          });\n        }\n\n        // 其他日期\n        return date.toLocaleDateString('zh-CN') + ' ' +\n               date.toLocaleTimeString('zh-CN', {\n                 hour: '2-digit',\n                 minute: '2-digit'\n               });\n      } catch (e) {\n        return timeStr;\n      }\n    },\n\n    // 获取头像URL\n    getAvatarUrl(message) {\n      if (message.flag == '2') {\n        // 医生头像 - 使用by2字段\n        return message.by2 = this.fileBasePath + message.by2;\n      } else {\n        // 患者头像 - 使用by1字段\n        return message.by1 = this.fileBasePath + message.by1 ;\n      }\n    },\n\n\n\n    // 处理图片加载错误\n    handleImageError(event) {\n      const img = event.target;\n      const messageElement = img.closest('.message-item');\n      const isDoctor = messageElement.classList.contains('doctor-message');\n      img.src = this.getDefaultAvatar(isDoctor ? 'doctor' : 'patient');\n    },\n\n    // 获取患者头像URL（用于聊天头部）\n    getPatientAvatarUrl() {\n      // 从最新的消息中获取患者头像\n      const patientMessage = this.messages.find(msg => msg.flag == '1');\n      if (patientMessage && patientMessage.by1) {\n        return this.fileBasePath + patientMessage.by1;\n      }\n    },\n\n    // 处理头部图片加载错误\n    handleHeaderImageError(event) {\n      event.target.src = this.getDefaultAvatar('patient');\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.stopAutoRefresh();\n      this.visible = false;\n      this.inputMessage = '';\n      this.messages = [];\n    }\n  },\n  beforeUnmount() {\n    this.stopAutoRefresh();\n  }\n};\n</script>\n\n<style scoped>\n/* 对话框整体样式 */\n.chat-dialog :deep(.el-dialog) {\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.chat-dialog :deep(.el-dialog__header) {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 20px 24px;\n  margin: 0;\n}\n\n.chat-dialog :deep(.el-dialog__title) {\n  color: white;\n  font-weight: 600;\n  font-size: 18px;\n}\n\n.chat-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {\n  color: white;\n  font-size: 20px;\n}\n\n.chat-dialog :deep(.el-dialog__body) {\n  padding: 0;\n  background: #f8f9fa;\n}\n\n/* 聊天头部 */\n.chat-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 24px;\n  background: white;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.patient-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.patient-info .avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 18px;\n  overflow: hidden;\n  border: 2px solid #fff;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.patient-info .avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  border-radius: 50%;\n}\n\n.patient-info .info .name {\n  font-weight: 600;\n  color: #333;\n  font-size: 16px;\n}\n\n.patient-info .info .status {\n  font-size: 12px;\n  color: #28a745;\n  margin-top: 2px;\n}\n\n.chat-actions .el-button {\n  color: #6c757d;\n}\n\n/* 聊天容器 */\n.chat-container {\n  height: 450px;\n  overflow-y: auto;\n  padding: 20px 24px;\n  background: #f8f9fa;\n}\n\n.empty-messages {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  color: #6c757d;\n}\n\n.empty-messages i {\n  font-size: 48px;\n  margin-bottom: 12px;\n  opacity: 0.5;\n}\n\n.empty-messages p {\n  margin: 0;\n  font-size: 14px;\n}\n\n/* 消息列表 */\n.chat-messages {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.message-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  max-width: 85%;\n}\n\n.patient-message {\n  align-self: flex-start;\n}\n\n.doctor-message {\n  align-self: flex-end;\n  flex-direction: row-reverse;\n}\n\n/* 头像 */\n.message-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  overflow: hidden;\n  border: 2px solid #fff;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.message-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  border-radius: 50%;\n}\n\n.patient-avatar {\n  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);\n}\n\n.doctor-avatar {\n  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);\n}\n\n/* 消息内容 */\n.message-wrapper {\n  flex: 1;\n  min-width: 0;\n}\n\n.message-content {\n  border-radius: 18px;\n  padding: 12px 16px;\n  position: relative;\n  word-wrap: break-word;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.patient-message .message-content {\n  background: white;\n  color: #333;\n  border-bottom-left-radius: 6px;\n}\n\n.doctor-message .message-content {\n  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);\n  color: white;\n  border-bottom-right-radius: 6px;\n}\n\n.message-text {\n  font-size: 14px;\n  line-height: 1.5;\n  word-break: break-word;\n}\n\n.message-time {\n  font-size: 11px;\n  margin-top: 4px;\n  opacity: 0.7;\n  text-align: center;\n}\n\n.doctor-message .message-time {\n  text-align: right;\n}\n\n.patient-message .message-time {\n  text-align: left;\n}\n\n/* 输入区域 */\n.chat-input {\n  background: white;\n  border-top: 1px solid #e9ecef;\n  padding: 0;\n}\n\n.input-wrapper {\n  padding: 20px 24px;\n}\n\n.message-input :deep(.el-textarea__inner) {\n  border: 2px solid #e9ecef;\n  border-radius: 12px;\n  padding: 12px 16px;\n  font-size: 14px;\n  line-height: 1.5;\n  resize: none;\n  transition: all 0.3s ease;\n}\n\n.message-input :deep(.el-textarea__inner):focus {\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.input-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 16px;\n}\n\n.input-tips {\n  display: flex;\n  gap: 16px;\n}\n\n.tip-item {\n  font-size: 12px;\n  color: #6c757d;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.tip-item i {\n  font-size: 14px;\n}\n\n.send-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.send-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  border-radius: 20px;\n  padding: 8px 20px;\n  font-weight: 600;\n}\n\n.send-btn:hover {\n  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n}\n\n.send-btn:disabled {\n  background: #e9ecef;\n  color: #6c757d;\n  transform: none;\n  box-shadow: none;\n}\n\n/* 滚动条样式 */\n.chat-container::-webkit-scrollbar {\n  width: 6px;\n}\n\n.chat-container::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n.chat-container::-webkit-scrollbar-thumb {\n  background: rgba(0,0,0,0.2);\n  border-radius: 3px;\n}\n\n.chat-container::-webkit-scrollbar-thumb:hover {\n  background: rgba(0,0,0,0.3);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .chat-dialog :deep(.el-dialog) {\n    width: 95% !important;\n    margin: 0 auto;\n  }\n\n  .chat-container {\n    height: 350px;\n    padding: 16px;\n  }\n\n  .input-wrapper {\n    padding: 16px;\n  }\n\n  .input-tips {\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .input-actions {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 12px;\n  }\n\n  .send-actions {\n    justify-content: flex-end;\n  }\n}\n</style>\n"], "mappings": ";;EAUSA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAQ;;;EAOdA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAM;;EAIhBA,KAAK,EAAC;AAAc;;EAQtBA,KAAK,EAAC,gBAAgB;EAACC,GAAG,EAAC;;;EACzBD,KAAK,EAAC;AAAe;;;EACUA,KAAK,EAAC;;;EASjCA,KAAK,EAAC;AAAgB;;;EAStBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAEtBA,KAAK,EAAC;AAAc;;EAO5BA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAe;;EAWnBA,KAAK,EAAC;AAAe;;EAWnBA,KAAK,EAAC;AAAc;;;;;uBArFjCE,YAAA,CAqGYC,oBAAA;gBApGDC,QAAA,CAAAC,OAAO;+DAAPD,QAAA,CAAAC,OAAO,GAAAC,MAAA;IACfC,KAAK,MAAMC,MAAA,CAAAC,WAAW;IACvBC,KAAK,EAAC,OAAO;IACZ,cAAY,EAAEN,QAAA,CAAAO,WAAW;IAC1BX,KAAK,EAAC,aAAa;IACnBY,GAAG,EAAC;;sBAEJ,MAAe,CAAfC,mBAAA,YAAe,EACfC,mBAAA,CAmBM,OAnBNC,UAmBM,GAlBJD,mBAAA,CAYM,OAZNE,UAYM,GAXJF,mBAAA,CAMM,OANNG,UAMM,GALJH,mBAAA,CAIE;MAHCI,GAAG,EAAEd,QAAA,CAAAe,mBAAmB;MACxBC,GAAG,EAAEZ,MAAA,CAAAC,WAAW;MAChBY,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEnB,QAAA,CAAAoB,sBAAA,IAAApB,QAAA,CAAAoB,sBAAA,IAAAD,IAAA,CAAsB;4DAGlCT,mBAAA,CAGM,OAHNW,UAGM,GAFJX,mBAAA,CAAyC,OAAzCY,UAAyC,EAAAC,gBAAA,CAApBnB,MAAA,CAAAC,WAAW,kB,0BAChCK,mBAAA,CAA8B;MAAzBd,KAAK,EAAC;IAAQ,GAAC,MAAI,qB,KAG5Bc,mBAAA,CAIM,OAJNc,UAIM,GAHJC,YAAA,CAEYC,oBAAA;MAFDC,IAAI,EAAC,OAAO;MAACC,IAAI,EAAC,MAAM;MAAEC,OAAK,EAAE7B,QAAA,CAAA8B;;wBAC1C,MAA+BZ,MAAA,QAAAA,MAAA,OAA/BR,mBAAA,CAA+B;QAA5Bd,KAAK,EAAC;MAAiB,4B,iBAAK,MACjC,E;;;wCAIJa,mBAAA,YAAe,EACfC,mBAAA,CA4BM,OA5BNqB,UA4BM,GA3BJrB,mBAAA,CA0BM,OA1BNsB,UA0BM,GAzBOC,KAAA,CAAAC,QAAQ,CAACC,MAAM,U,cAA1BC,mBAAA,CAGM,OAHNC,WAGM,EAAAnB,MAAA,QAAAA,MAAA,OAFJR,mBAAA,CAAsC;MAAnCd,KAAK,EAAC;IAAwB,4BACjCc,mBAAA,CAAa,WAAV,QAAM,oB,4DAEX0B,mBAAA,CAoBME,SAAA,QAAAC,WAAA,CAnBuBN,KAAA,CAAAC,QAAQ,GAA3BM,OAAO,EAAEC,KAAK;2BADxBL,mBAAA,CAoBM;QAlBHM,GAAG,EAAED,KAAK;QACV7C,KAAK,EAAA+C,eAAA,kBAAmBH,OAAO,CAACI,IAAI;UAErClC,mBAAA,CAQM,OARNmC,WAQM,GAPJnC,mBAAA,CAMM;QANDd,KAAK,EAAA+C,eAAA,EAAC,gBAAgB,EAASH,OAAO,CAACI,IAAI;UAC9ClC,mBAAA,CAIE;QAHCI,GAAG,EAAEd,QAAA,CAAA8C,YAAY,CAACN,OAAO;QACzBxB,GAAG,EAAEwB,OAAO,CAACI,IAAI;QACjB3B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEnB,QAAA,CAAA+C,gBAAA,IAAA/C,QAAA,CAAA+C,gBAAA,IAAA5B,IAAA,CAAgB;gFAI9BT,mBAAA,CAKM,OALNsC,WAKM,GAJJtC,mBAAA,CAEM,OAFNuC,WAEM,GADJvC,mBAAA,CAAqD,OAArDwC,WAAqD,EAAA3B,gBAAA,CAAxBiB,OAAO,CAACW,OAAO,iB,GAE9CzC,mBAAA,CAAkE,OAAlE0C,WAAkE,EAAA7B,gBAAA,CAArCvB,QAAA,CAAAqD,UAAU,CAACb,OAAO,CAACc,QAAQ,kB;8DAMhE7C,mBAAA,UAAa,EACbC,mBAAA,CAsCM,OAtCN6C,WAsCM,GArCJ7C,mBAAA,CAoCM,OApCN8C,WAoCM,GAnCJ/B,YAAA,CASYgC,mBAAA;kBARDxB,KAAA,CAAAyB,YAAY;iEAAZzB,KAAA,CAAAyB,YAAY,GAAAxD,MAAA;MACrB0B,IAAI,EAAC,UAAU;MACd+B,IAAI,EAAE,CAAC;MACRC,WAAW,EAAC,YAAY;MACxBC,SAAS,EAAC,KAAK;MACf,iBAAe,EAAf,EAAe;MACdC,OAAK,EAAAC,SAAA,CAAAC,cAAA,CAAahE,QAAA,CAAAiE,WAAW;MAC9BrE,KAAK,EAAC;wDAERc,mBAAA,CAwBM,OAxBNwD,WAwBM,G,4BAvBJxD,mBAAA,CASM;MATDd,KAAK,EAAC;IAAY,IACrBc,mBAAA,CAGO;MAHDd,KAAK,EAAC;IAAU,IACpBc,mBAAA,CAA4B;MAAzBd,KAAK,EAAC;IAAc,I,iBAAK,qBAE9B,E,GACAc,mBAAA,CAGO;MAHDd,KAAK,EAAC;IAAU,IACpBc,mBAAA,CAA4B;MAAzBd,KAAK,EAAC;IAAc,I,iBAAK,WAE9B,E,wBAEFc,mBAAA,CAYM,OAZNyD,WAYM,GAXJ1C,YAAA,CAAiEC,oBAAA;MAArDG,OAAK,EAAAX,MAAA,QAAAA,MAAA,MAAAhB,MAAA,IAAE+B,KAAA,CAAAyB,YAAY;MAAO/B,IAAI,EAAC;;wBAAQ,MAAET,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;QACrDO,YAAA,CASYC,oBAAA;MARVE,IAAI,EAAC,SAAS;MACbC,OAAK,EAAE7B,QAAA,CAAAiE,WAAW;MAClBG,OAAO,EAAEnC,KAAA,CAAAoC,OAAO;MAChBC,QAAQ,GAAGrC,KAAA,CAAAyB,YAAY,CAACa,IAAI;MAC7B3E,KAAK,EAAC;;wBAEN,MAAmCsB,MAAA,QAAAA,MAAA,OAAnCR,mBAAA,CAAmC;QAAhCd,KAAK,EAAC;MAAqB,4B,iBAAK,MAErC,E", "ignoreList": []}]}