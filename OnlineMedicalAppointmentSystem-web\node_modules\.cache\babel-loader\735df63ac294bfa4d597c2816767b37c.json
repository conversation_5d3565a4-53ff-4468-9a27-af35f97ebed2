{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue?vue&type=template&id=795f54f0&scoped=true", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue", "mtime": 1749361137943}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "ref", "_createBlock", "_component_el_dialog", "$options", "visible", "$event", "title", "$props", "patientName", "width", "handleClose", "top", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "_hoisted_5", "_createVNode", "_component_el_button", "size", "type", "onClick", "loadMessages", "_cache", "_hoisted_6", "_hoisted_7", "$data", "messages", "length", "_createElementBlock", "_hoisted_8", "_Fragment", "_renderList", "message", "index", "key", "_normalizeClass", "flag", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "content", "_hoisted_13", "_ctx", "formatTime", "sendtime", "_hoisted_14", "_hoisted_15", "_component_el_input", "inputMessage", "rows", "placeholder", "maxlength", "onKeyup", "_with<PERSON><PERSON><PERSON>", "_withModifiers", "sendMessage", "_hoisted_16", "_hoisted_17", "loading", "sending", "disabled", "trim"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    v-model=\"visible\"\n    :title=\"`与${patientName}的对话`\"\n    width=\"700px\"\n    :before-close=\"handleClose\"\n    class=\"chat-dialog\"\n    top=\"5vh\"\n  >\n    <!-- 聊天头部信息 -->\n    <div class=\"chat-header\">\n      <div class=\"patient-info\">\n        <div class=\"avatar\">\n          <i class=\"el-icon-user-solid\"></i>\n        </div>\n        <div class=\"info\">\n          <div class=\"name\">{{ patientName }}</div>\n          <div class=\"status\">在线咨询</div>\n        </div>\n      </div>\n      <div class=\"chat-actions\">\n        <el-button size=\"small\" type=\"text\" @click=\"loadMessages\">\n          <i class=\"el-icon-refresh\"></i> 刷新\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 聊天消息区域 -->\n    <div class=\"chat-container\" ref=\"chatContainer\">\n      <div class=\"chat-messages\">\n        <div v-if=\"messages.length === 0\" class=\"empty-messages\">\n          <i class=\"el-icon-chat-dot-round\"></i>\n          <p>暂无聊天记录</p>\n        </div>\n        <div\n          v-for=\"(message, index) in messages\"\n          :key=\"index\"\n          :class=\"['message-item', message.flag == '2' ? 'doctor-message' : 'patient-message']\"\n        >\n          <div class=\"avatar-wrapper\">\n            <div class=\"message-avatar\" :class=\"message.flag == '2' ? 'doctor-avatar' : 'patient-avatar'\">\n              <i :class=\"message.flag == '2' ? 'el-icon-s-custom' : 'el-icon-user-solid'\"></i>\n            </div>\n          </div>\n          <div class=\"message-wrapper\">\n            <div class=\"message-content\">\n              <div class=\"message-text\">{{ message.content }}</div>\n            </div>\n            <div class=\"message-time\">{{ formatTime(message.sendtime) }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 输入区域 -->\n    <div class=\"chat-input\">\n      <div class=\"input-wrapper\">\n        <el-input\n          v-model=\"inputMessage\"\n          type=\"textarea\"\n          :rows=\"3\"\n          placeholder=\"请输入回复内容...\"\n          maxlength=\"500\"\n          show-word-limit\n          @keyup.ctrl.enter=\"sendMessage\"\n          class=\"message-input\"\n        ></el-input>\n        <div class=\"input-actions\">\n          <div class=\"input-tips\">\n            <span class=\"tip-item\">\n              <i class=\"el-icon-info\"></i>\n              Ctrl + Enter 快速发送\n            </span>\n            <span class=\"tip-item\">\n              <i class=\"el-icon-time\"></i>\n              每5秒自动刷新\n            </span>\n          </div>\n          <div class=\"send-actions\">\n            <el-button @click=\"inputMessage = ''\" size=\"small\">清空</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"sendMessage\"\n              :loading=\"sending\"\n              :disabled=\"!inputMessage.trim()\"\n              class=\"send-btn\"\n            >\n              <i class=\"el-icon-s-promotion\"></i>\n              发送\n            </el-button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport request, { base } from \"../../utils/http\";\n\nexport default {\n  name: 'ChatDialog',\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    patientName: {\n      type: String,\n      default: ''\n    },\n    patientId: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['update:modelValue', 'message-sent'],\n  data() {\n    return {\n      messages: [],\n      inputMessage: '',\n      sending: false,\n      refreshTimer: null\n    };\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.modelValue;\n      },\n      set(value) {\n        this.$emit('update:modelValue', value);\n      }\n    }\n  },\n  watch: {\n    visible(newVal) {\n      if (newVal) {\n        this.loadMessages();\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    }\n  },\n  methods: {\n    // 加载聊天消息\n    loadMessages() {\n      if (!this.patientId) return;\n\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      const param = {\n        lname: this.patientName,\n        did: user.did\n      };\n\n      const url = base + \"/chatinfo/list2?currentPage=1&pageSize=5000\";\n      request.post(url, param).then((res) => {\n        if (res.resdata) {\n          this.messages = res.resdata.sort((a, b) => {\n            return new Date(a.sendtime) - new Date(b.sendtime);\n          });\n          this.$nextTick(() => {\n            this.scrollToBottom();\n          });\n        }\n      }).catch((error) => {\n        console.error('加载聊天消息失败:', error);\n        this.$message.error('加载聊天消息失败');\n      });\n    },\n\n    // 发送消息\n    sendMessage() {\n      if (!this.inputMessage.trim()) {\n        this.$message.warning('请输入消息内容');\n        return;\n      }\n\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      this.sending = true;\n\n      const param = {\n        lname: this.patientName,\n        did: user.did,\n        content: this.inputMessage,\n        flag: \"2\", // 医生发送的消息\n        sendtime: new Date().toLocaleString()\n      };\n\n      const url = base + \"/chatinfo/add\";\n      request.post(url, param).then((res) => {\n        this.sending = false;\n        this.inputMessage = '';\n        this.$message.success('发送成功');\n\n        // 立即刷新消息列表\n        setTimeout(() => {\n          this.loadMessages();\n        }, 500);\n\n        // 通知父组件消息已发送\n        this.$emit('message-sent');\n      }).catch((error) => {\n        this.sending = false;\n        console.error('发送消息失败:', error);\n        this.$message.error('发送消息失败');\n      });\n    },\n\n    // 滚动到底部\n    scrollToBottom() {\n      const container = this.$refs.chatContainer;\n      if (container) {\n        container.scrollTop = container.scrollHeight;\n      }\n    },\n\n    // 开始自动刷新\n    startAutoRefresh() {\n      this.stopAutoRefresh(); // 先清除之前的定时器\n      this.refreshTimer = setInterval(() => {\n        this.loadMessages();\n      }, 5000); // 每5秒刷新一次\n    },\n\n    // 停止自动刷新\n    stopAutoRefresh() {\n      if (this.refreshTimer) {\n        clearInterval(this.refreshTimer);\n        this.refreshTimer = null;\n      }\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.stopAutoRefresh();\n      this.visible = false;\n      this.inputMessage = '';\n      this.messages = [];\n    }\n  },\n  beforeUnmount() {\n    this.stopAutoRefresh();\n  }\n};\n</script>\n\n<style scoped>\n.chat-dialog {\n  .el-dialog__body {\n    padding: 0;\n  }\n}\n\n.chat-container {\n  height: 400px;\n  overflow-y: auto;\n  padding: 20px;\n  background-color: #f5f5f5;\n}\n\n.chat-messages {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.message-item {\n  display: flex;\n  max-width: 80%;\n}\n\n.patient-message {\n  justify-content: flex-start;\n}\n\n.doctor-message {\n  justify-content: flex-end;\n  align-self: flex-end;\n}\n\n.message-content {\n  background: white;\n  border-radius: 12px;\n  padding: 12px 16px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  position: relative;\n}\n\n.doctor-message .message-content {\n  background: #07c160;\n  color: white;\n}\n\n.patient-message .message-content::before {\n  content: '';\n  position: absolute;\n  left: -8px;\n  top: 12px;\n  width: 0;\n  height: 0;\n  border-top: 8px solid transparent;\n  border-bottom: 8px solid transparent;\n  border-right: 8px solid white;\n}\n\n.doctor-message .message-content::before {\n  content: '';\n  position: absolute;\n  right: -8px;\n  top: 12px;\n  width: 0;\n  height: 0;\n  border-top: 8px solid transparent;\n  border-bottom: 8px solid transparent;\n  border-left: 8px solid #07c160;\n}\n\n.message-text {\n  font-size: 14px;\n  line-height: 1.4;\n  word-wrap: break-word;\n}\n\n.message-time {\n  font-size: 12px;\n  margin-top: 8px;\n  opacity: 0.7;\n}\n\n.doctor-message .message-time {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.patient-message .message-time {\n  color: #999;\n}\n\n.chat-input {\n  padding: 20px;\n  border-top: 1px solid #eee;\n  background: white;\n}\n\n.input-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 10px;\n}\n\n.input-tip {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 滚动条样式 */\n.chat-container::-webkit-scrollbar {\n  width: 6px;\n}\n\n.chat-container::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.chat-container::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.chat-container::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n</style>\n"], "mappings": ";;EAUSA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAc;;EAIlBA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAM;;EAIhBA,KAAK,EAAC;AAAc;;EAQtBA,KAAK,EAAC,gBAAgB;EAACC,GAAG,EAAC;;;EACzBD,KAAK,EAAC;AAAe;;;EACUA,KAAK,EAAC;;;EASjCA,KAAK,EAAC;AAAgB;;EAKtBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAEtBA,KAAK,EAAC;AAAc;;EAO5BA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAe;;EAWnBA,KAAK,EAAC;AAAe;;EAWnBA,KAAK,EAAC;AAAc;;;;;uBA7EjCE,YAAA,CA6FYC,oBAAA;gBA5FDC,QAAA,CAAAC,OAAO;+DAAPD,QAAA,CAAAC,OAAO,GAAAC,MAAA;IACfC,KAAK,MAAMC,MAAA,CAAAC,WAAW;IACvBC,KAAK,EAAC,OAAO;IACZ,cAAY,EAAEN,QAAA,CAAAO,WAAW;IAC1BX,KAAK,EAAC,aAAa;IACnBY,GAAG,EAAC;;sBAEJ,MAAe,CAAfC,mBAAA,YAAe,EACfC,mBAAA,CAeM,OAfNC,UAeM,GAdJD,mBAAA,CAQM,OARNE,UAQM,G,0BAPJF,mBAAA,CAEM;MAFDd,KAAK,EAAC;IAAQ,IACjBc,mBAAA,CAAkC;MAA/Bd,KAAK,EAAC;IAAoB,G,sBAE/Bc,mBAAA,CAGM,OAHNG,UAGM,GAFJH,mBAAA,CAAyC,OAAzCI,UAAyC,EAAAC,gBAAA,CAApBX,MAAA,CAAAC,WAAW,kB,0BAChCK,mBAAA,CAA8B;MAAzBd,KAAK,EAAC;IAAQ,GAAC,MAAI,qB,KAG5Bc,mBAAA,CAIM,OAJNM,UAIM,GAHJC,YAAA,CAEYC,oBAAA;MAFDC,IAAI,EAAC,OAAO;MAACC,IAAI,EAAC,MAAM;MAAEC,OAAK,EAAErB,QAAA,CAAAsB;;wBAC1C,MAA+BC,MAAA,QAAAA,MAAA,OAA/Bb,mBAAA,CAA+B;QAA5Bd,KAAK,EAAC;MAAiB,4B,iBAAK,MACjC,E;;;wCAIJa,mBAAA,YAAe,EACfC,mBAAA,CAwBM,OAxBNc,UAwBM,GAvBJd,mBAAA,CAsBM,OAtBNe,UAsBM,GArBOC,KAAA,CAAAC,QAAQ,CAACC,MAAM,U,cAA1BC,mBAAA,CAGM,OAHNC,UAGM,EAAAP,MAAA,QAAAA,MAAA,OAFJb,mBAAA,CAAsC;MAAnCd,KAAK,EAAC;IAAwB,4BACjCc,mBAAA,CAAa,WAAV,QAAM,oB,4DAEXmB,mBAAA,CAgBME,SAAA,QAAAC,WAAA,CAfuBN,KAAA,CAAAC,QAAQ,GAA3BM,OAAO,EAAEC,KAAK;2BADxBL,mBAAA,CAgBM;QAdHM,GAAG,EAAED,KAAK;QACVtC,KAAK,EAAAwC,eAAA,kBAAmBH,OAAO,CAACI,IAAI;UAErC3B,mBAAA,CAIM,OAJN4B,UAIM,GAHJ5B,mBAAA,CAEM;QAFDd,KAAK,EAAAwC,eAAA,EAAC,gBAAgB,EAASH,OAAO,CAACI,IAAI;UAC9C3B,mBAAA,CAAgF;QAA5Ed,KAAK,EAAAwC,eAAA,CAAEH,OAAO,CAACI,IAAI;kDAG3B3B,mBAAA,CAKM,OALN6B,WAKM,GAJJ7B,mBAAA,CAEM,OAFN8B,WAEM,GADJ9B,mBAAA,CAAqD,OAArD+B,WAAqD,EAAA1B,gBAAA,CAAxBkB,OAAO,CAACS,OAAO,iB,GAE9ChC,mBAAA,CAAkE,OAAlEiC,WAAkE,EAAA5B,gBAAA,CAArC6B,IAAA,CAAAC,UAAU,CAACZ,OAAO,CAACa,QAAQ,kB;8DAMhErC,mBAAA,UAAa,EACbC,mBAAA,CAsCM,OAtCNqC,WAsCM,GArCJrC,mBAAA,CAoCM,OApCNsC,WAoCM,GAnCJ/B,YAAA,CASYgC,mBAAA;kBARDvB,KAAA,CAAAwB,YAAY;iEAAZxB,KAAA,CAAAwB,YAAY,GAAAhD,MAAA;MACrBkB,IAAI,EAAC,UAAU;MACd+B,IAAI,EAAE,CAAC;MACRC,WAAW,EAAC,YAAY;MACxBC,SAAS,EAAC,KAAK;MACf,iBAAe,EAAf,EAAe;MACdC,OAAK,EAAAC,SAAA,CAAAC,cAAA,CAAaxD,QAAA,CAAAyD,WAAW;MAC9B7D,KAAK,EAAC;wDAERc,mBAAA,CAwBM,OAxBNgD,WAwBM,G,0BAvBJhD,mBAAA,CASM;MATDd,KAAK,EAAC;IAAY,IACrBc,mBAAA,CAGO;MAHDd,KAAK,EAAC;IAAU,IACpBc,mBAAA,CAA4B;MAAzBd,KAAK,EAAC;IAAc,I,iBAAK,qBAE9B,E,GACAc,mBAAA,CAGO;MAHDd,KAAK,EAAC;IAAU,IACpBc,mBAAA,CAA4B;MAAzBd,KAAK,EAAC;IAAc,I,iBAAK,WAE9B,E,wBAEFc,mBAAA,CAYM,OAZNiD,WAYM,GAXJ1C,YAAA,CAAiEC,oBAAA;MAArDG,OAAK,EAAAE,MAAA,QAAAA,MAAA,MAAArB,MAAA,IAAEwB,KAAA,CAAAwB,YAAY;MAAO/B,IAAI,EAAC;;wBAAQ,MAAEI,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;QACrDN,YAAA,CASYC,oBAAA;MARVE,IAAI,EAAC,SAAS;MACbC,OAAK,EAAErB,QAAA,CAAAyD,WAAW;MAClBG,OAAO,EAAElC,KAAA,CAAAmC,OAAO;MAChBC,QAAQ,GAAGpC,KAAA,CAAAwB,YAAY,CAACa,IAAI;MAC7BnE,KAAK,EAAC;;wBAEN,MAAmC2B,MAAA,QAAAA,MAAA,OAAnCb,mBAAA,CAAmC;QAAhCd,KAAK,EAAC;MAAqB,4B,iBAAK,MAErC,E", "ignoreList": []}]}