{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\chatinfo\\ChatinfoManage.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\chatinfo\\ChatinfoManage.vue", "mtime": 1749360950885}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "ChatDialog", "name", "components", "data", "page", "currentPage", "pageSize", "totalCount", "isClear", "listLoading", "btnLoading", "datalist", "chatDialogVisible", "selectedPatient", "id", "refreshTimer", "created", "getDatas", "startAutoRefresh", "beforeUnmount", "stopAutoRefresh", "methods", "handleDelete", "index", "row", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "url", "cid", "post", "res", "$message", "message", "offset", "catch", "handleCurrentChange", "val", "user", "JSON", "parse", "sessionStorage", "getItem", "para", "did", "condition", "resdata", "length", "isPage", "count", "error", "console", "handleShow", "$router", "push", "path", "query", "handleReply", "lname", "onMessageSent", "setTimeout", "setInterval", "clearInterval", "handleEdit"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\chatinfo\\ChatinfoManage.vue"], "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n      <div class=\"page-header\">\n        <h3>我收到的聊天</h3>\n        <p>管理患者发送的聊天消息，点击回复按钮进行在线沟通</p>\n      </div>\n\n<el-table :data=\"datalist\" border stripe style=\"width: 100%\"  v-loading=\"listLoading\"   highlight-current-row   max-height=\"600\"     size=\"small\">\n<el-table-column prop=\"lname\" label=\"患者姓名\"  align=\"center\" width=\"120\"></el-table-column>\n<el-table-column prop=\"content\" label=\"最新消息\"  align=\"center\" min-width=\"200\">\n  <template #default=\"scope\">\n    <div class=\"message-content\">\n      <span :class=\"{'unread-message': scope.row.flag == '1'}\">{{ scope.row.content }}</span>\n      <el-tag v-if=\"scope.row.flag == '1'\" type=\"danger\" size=\"small\" style=\"margin-left: 8px;\">未回复</el-tag>\n    </div>\n  </template>\n</el-table-column>\n<el-table-column prop=\"sendtime\" label=\"发送时间\"  align=\"center\" width=\"160\"></el-table-column>\n<el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n<template #default=\"scope\">\n<el-button type=\"primary\" size=\"mini\" @click=\"handleReply(scope.$index, scope.row)\" icon=\"el-icon-chat-dot-round\" style=\"padding: 3px 6px 3px 6px;\">回复</el-button>\n<el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\" style=\" padding: 3px 6px 3px 6px;\">删除</el-button>\n</template>\n</el-table-column>\n</el-table>\n<el-pagination  @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\" \n background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n\n    <!-- 聊天对话框 -->\n    <ChatDialog\n      v-model=\"chatDialogVisible\"\n      :patient-name=\"selectedPatient.name\"\n      :patient-id=\"selectedPatient.id\"\n      @message-sent=\"onMessageSent\"\n    />\n\n    </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nimport ChatDialog from \"../../../components/ChatDialog.vue\";\n\nexport default {\n  name: 'chatinfo',\n  components: {\n    ChatDialog\n  },\n    data() {\n      return {\n       \n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,      \n        \n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据\n        chatDialogVisible: false, //聊天对话框显示状态\n        selectedPatient: { //选中的患者信息\n          name: '',\n          id: ''\n        },\n        refreshTimer: null //自动刷新定时器\n\n      };\n    },\n    created() {\n      this.getDatas();\n      this.startAutoRefresh();\n    },\n\n    beforeUnmount() {\n      this.stopAutoRefresh();\n    },\n\n \n    methods: {    \n\n              \n       // 删除聊天\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/chatinfo/del?id=\" + row.cid;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n        },\n                \n        // 分页\n        handleCurrentChange(val) {\n          this.page.currentPage = val;\n          this.getDatas();\n        },     \n     \n        //获取列表数据\n      getDatas() {\n           var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息\n        let para = {\n            did:user.did,\n            condition:\" and a.cid in(select max(cid) from chatinfo where did=\"+user.did+\" group by lname ) \"\n          };\n          this.listLoading = true;\n          let url = base + \"/chatinfo/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;\n          request.post(url, para).then((res) => {\n            if (res.resdata.length > 0) {\n              this.isPage = true;\n            } else {\n              this.isPage = false;\n            }\n            this.page.totalCount = res.count;\n            this.datalist = res.resdata;\n            this.listLoading = false;\n          }).catch((error) => {\n            console.error('获取聊天信息列表失败:', error);\n            this.listLoading = false;\n            this.$message.error('获取聊天信息列表失败');\n          });\n        },\n        \n           \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/ChatinfoDetail\",\n             query: {\n                id: row.cid,\n              },\n          });\n        },\n    \n        // 回复聊天\n        handleReply(index, row) {\n          this.selectedPatient = {\n            name: row.lname,\n            id: row.lname // 使用用户名作为标识\n          };\n          this.chatDialogVisible = true;\n        },\n\n        // 消息发送成功回调\n        onMessageSent() {\n          // 刷新聊天列表\n          setTimeout(() => {\n            this.getDatas();\n          }, 1000);\n        },\n\n        // 开始自动刷新\n        startAutoRefresh() {\n          this.stopAutoRefresh(); // 先清除之前的定时器\n          this.refreshTimer = setInterval(() => {\n            this.getDatas();\n          }, 30000); // 每30秒刷新一次列表\n        },\n\n        // 停止自动刷新\n        stopAutoRefresh() {\n          if (this.refreshTimer) {\n            clearInterval(this.refreshTimer);\n            this.refreshTimer = null;\n          }\n        },\n\n        // 编辑\n        handleEdit(index, row) {\n          this.$router.push({\n            path: \"/ChatinfoEdit\",\n             query: {\n                id: row.cid,\n              },\n          });\n        },\n      },\n}\n\n</script>\n<style scoped>\n.page-header {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.page-header h3 {\n  margin: 0 0 8px 0;\n  color: #333;\n  font-size: 20px;\n}\n\n.page-header p {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.message-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.unread-message {\n  font-weight: bold;\n  color: #e74c3c;\n}\n\n/* 表格样式优化 */\n.el-table {\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.el-table th {\n  background-color: #f8f9fa;\n  color: #495057;\n  font-weight: 600;\n}\n\n.el-button {\n  border-radius: 4px;\n}\n\n.el-button--primary {\n  background-color: #007bff;\n  border-color: #007bff;\n}\n\n.el-button--primary:hover {\n  background-color: #0056b3;\n  border-color: #0056b3;\n}\n\n.el-button--danger {\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n\n.el-button--danger:hover {\n  background-color: #c82333;\n  border-color: #c82333;\n}\n</style>\n \n\n"], "mappings": ";AAyCA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,OAAOC,UAAS,MAAO,oCAAoC;AAE3D,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE;IACVF;EACF,CAAC;EACCG,IAAIA,CAAA,EAAG;IACL,OAAO;MAELC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;MACDC,OAAO,EAAE,KAAK;MAEdC,WAAW,EAAE,KAAK;MAAE;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,EAAE;MAAE;MACdC,iBAAiB,EAAE,KAAK;MAAE;MAC1BC,eAAe,EAAE;QAAE;QACjBZ,IAAI,EAAE,EAAE;QACRa,EAAE,EAAE;MACN,CAAC;MACDC,YAAY,EAAE,IAAG,CAAE;IAErB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACC,gBAAgB,CAAC,CAAC;EACzB,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd,IAAI,CAACC,eAAe,CAAC,CAAC;EACxB,CAAC;EAGDC,OAAO,EAAE;IAGN;IACCC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACvB,IAAI,CAACC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE;QAC/BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACV,IAAI,CAACpB,WAAU,GAAI,IAAI;QACvB,IAAIqB,GAAE,GAAI/B,IAAG,GAAI,mBAAkB,GAAIyB,GAAG,CAACO,GAAG;QAC9CjC,OAAO,CAACkC,IAAI,CAACF,GAAG,CAAC,CAACD,IAAI,CAAEI,GAAG,IAAK;UAC9B,IAAI,CAACxB,WAAU,GAAI,KAAK;UAExB,IAAI,CAACyB,QAAQ,CAAC;YACZC,OAAO,EAAE,MAAM;YACfP,IAAI,EAAE,SAAS;YACfQ,MAAM,EAAE;UACV,CAAC,CAAC;UACF,IAAI,CAACnB,QAAQ,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EACAoB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB,CAAC;IAED;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAACnC,IAAI,CAACC,WAAU,GAAIkC,GAAG;MAC3B,IAAI,CAACtB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACFA,QAAQA,CAAA,EAAG;MACN,IAAIuB,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;MAC1D,IAAIC,IAAG,GAAI;QACPC,GAAG,EAACN,IAAI,CAACM,GAAG;QACZC,SAAS,EAAC,wDAAwD,GAACP,IAAI,CAACM,GAAG,GAAC;MAC9E,CAAC;MACD,IAAI,CAACrC,WAAU,GAAI,IAAI;MACvB,IAAIqB,GAAE,GAAI/B,IAAG,GAAI,6BAA4B,GAAI,IAAI,CAACK,IAAI,CAACC,WAAW,GAAE,YAAW,GAAI,IAAI,CAACD,IAAI,CAACE,QAAQ;MACzGR,OAAO,CAACkC,IAAI,CAACF,GAAG,EAAEe,IAAI,CAAC,CAAChB,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACe,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UAC1B,IAAI,CAACC,MAAK,GAAI,IAAI;QACpB,OAAO;UACL,IAAI,CAACA,MAAK,GAAI,KAAK;QACrB;QACA,IAAI,CAAC9C,IAAI,CAACG,UAAS,GAAI0B,GAAG,CAACkB,KAAK;QAChC,IAAI,CAACxC,QAAO,GAAIsB,GAAG,CAACe,OAAO;QAC3B,IAAI,CAACvC,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC,CAAC4B,KAAK,CAAEe,KAAK,IAAK;QAClBC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;QACnC,IAAI,CAAC3C,WAAU,GAAI,KAAK;QACxB,IAAI,CAACyB,QAAQ,CAACkB,KAAK,CAAC,YAAY,CAAC;MACnC,CAAC,CAAC;IACJ,CAAC;IAGD;IACAE,UAAUA,CAAC/B,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAAC+B,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,iBAAiB;QACtBC,KAAK,EAAE;UACJ5C,EAAE,EAAEU,GAAG,CAACO;QACV;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACA4B,WAAWA,CAACpC,KAAK,EAAEC,GAAG,EAAE;MACtB,IAAI,CAACX,eAAc,GAAI;QACrBZ,IAAI,EAAEuB,GAAG,CAACoC,KAAK;QACf9C,EAAE,EAAEU,GAAG,CAACoC,KAAI,CAAE;MAChB,CAAC;MACD,IAAI,CAAChD,iBAAgB,GAAI,IAAI;IAC/B,CAAC;IAED;IACAiD,aAAaA,CAAA,EAAG;MACd;MACAC,UAAU,CAAC,MAAM;QACf,IAAI,CAAC7C,QAAQ,CAAC,CAAC;MACjB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IAED;IACAC,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACE,eAAe,CAAC,CAAC,EAAE;MACxB,IAAI,CAACL,YAAW,GAAIgD,WAAW,CAAC,MAAM;QACpC,IAAI,CAAC9C,QAAQ,CAAC,CAAC;MACjB,CAAC,EAAE,KAAK,CAAC,EAAE;IACb,CAAC;IAED;IACAG,eAAeA,CAAA,EAAG;MAChB,IAAI,IAAI,CAACL,YAAY,EAAE;QACrBiD,aAAa,CAAC,IAAI,CAACjD,YAAY,CAAC;QAChC,IAAI,CAACA,YAAW,GAAI,IAAI;MAC1B;IACF,CAAC;IAED;IACAkD,UAAUA,CAAC1C,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAAC+B,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,eAAe;QACpBC,KAAK,EAAE;UACJ5C,EAAE,EAAEU,GAAG,CAACO;QACV;MACJ,CAAC,CAAC;IACJ;EACF;AACN", "ignoreList": []}]}