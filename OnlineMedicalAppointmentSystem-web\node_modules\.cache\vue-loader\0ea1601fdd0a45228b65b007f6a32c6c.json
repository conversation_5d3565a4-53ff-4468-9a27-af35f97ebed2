{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue?vue&type=template&id=795f54f0&scoped=true", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue", "mtime": 1749361137943}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACV;IACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtF;UACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3F,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB;cACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/components/ChatDialog.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <el-dialog\n    v-model=\"visible\"\n    :title=\"`与${patientName}的对话`\"\n    width=\"700px\"\n    :before-close=\"handleClose\"\n    class=\"chat-dialog\"\n    top=\"5vh\"\n  >\n    <!-- 聊天头部信息 -->\n    <div class=\"chat-header\">\n      <div class=\"patient-info\">\n        <div class=\"avatar\">\n          <i class=\"el-icon-user-solid\"></i>\n        </div>\n        <div class=\"info\">\n          <div class=\"name\">{{ patientName }}</div>\n          <div class=\"status\">在线咨询</div>\n        </div>\n      </div>\n      <div class=\"chat-actions\">\n        <el-button size=\"small\" type=\"text\" @click=\"loadMessages\">\n          <i class=\"el-icon-refresh\"></i> 刷新\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 聊天消息区域 -->\n    <div class=\"chat-container\" ref=\"chatContainer\">\n      <div class=\"chat-messages\">\n        <div v-if=\"messages.length === 0\" class=\"empty-messages\">\n          <i class=\"el-icon-chat-dot-round\"></i>\n          <p>暂无聊天记录</p>\n        </div>\n        <div\n          v-for=\"(message, index) in messages\"\n          :key=\"index\"\n          :class=\"['message-item', message.flag == '2' ? 'doctor-message' : 'patient-message']\"\n        >\n          <div class=\"avatar-wrapper\">\n            <div class=\"message-avatar\" :class=\"message.flag == '2' ? 'doctor-avatar' : 'patient-avatar'\">\n              <i :class=\"message.flag == '2' ? 'el-icon-s-custom' : 'el-icon-user-solid'\"></i>\n            </div>\n          </div>\n          <div class=\"message-wrapper\">\n            <div class=\"message-content\">\n              <div class=\"message-text\">{{ message.content }}</div>\n            </div>\n            <div class=\"message-time\">{{ formatTime(message.sendtime) }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 输入区域 -->\n    <div class=\"chat-input\">\n      <div class=\"input-wrapper\">\n        <el-input\n          v-model=\"inputMessage\"\n          type=\"textarea\"\n          :rows=\"3\"\n          placeholder=\"请输入回复内容...\"\n          maxlength=\"500\"\n          show-word-limit\n          @keyup.ctrl.enter=\"sendMessage\"\n          class=\"message-input\"\n        ></el-input>\n        <div class=\"input-actions\">\n          <div class=\"input-tips\">\n            <span class=\"tip-item\">\n              <i class=\"el-icon-info\"></i>\n              Ctrl + Enter 快速发送\n            </span>\n            <span class=\"tip-item\">\n              <i class=\"el-icon-time\"></i>\n              每5秒自动刷新\n            </span>\n          </div>\n          <div class=\"send-actions\">\n            <el-button @click=\"inputMessage = ''\" size=\"small\">清空</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"sendMessage\"\n              :loading=\"sending\"\n              :disabled=\"!inputMessage.trim()\"\n              class=\"send-btn\"\n            >\n              <i class=\"el-icon-s-promotion\"></i>\n              发送\n            </el-button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport request, { base } from \"../../utils/http\";\n\nexport default {\n  name: 'ChatDialog',\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    patientName: {\n      type: String,\n      default: ''\n    },\n    patientId: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['update:modelValue', 'message-sent'],\n  data() {\n    return {\n      messages: [],\n      inputMessage: '',\n      sending: false,\n      refreshTimer: null\n    };\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.modelValue;\n      },\n      set(value) {\n        this.$emit('update:modelValue', value);\n      }\n    }\n  },\n  watch: {\n    visible(newVal) {\n      if (newVal) {\n        this.loadMessages();\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    }\n  },\n  methods: {\n    // 加载聊天消息\n    loadMessages() {\n      if (!this.patientId) return;\n\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      const param = {\n        lname: this.patientName,\n        did: user.did\n      };\n\n      const url = base + \"/chatinfo/list2?currentPage=1&pageSize=5000\";\n      request.post(url, param).then((res) => {\n        if (res.resdata) {\n          this.messages = res.resdata.sort((a, b) => {\n            return new Date(a.sendtime) - new Date(b.sendtime);\n          });\n          this.$nextTick(() => {\n            this.scrollToBottom();\n          });\n        }\n      }).catch((error) => {\n        console.error('加载聊天消息失败:', error);\n        this.$message.error('加载聊天消息失败');\n      });\n    },\n\n    // 发送消息\n    sendMessage() {\n      if (!this.inputMessage.trim()) {\n        this.$message.warning('请输入消息内容');\n        return;\n      }\n\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      this.sending = true;\n\n      const param = {\n        lname: this.patientName,\n        did: user.did,\n        content: this.inputMessage,\n        flag: \"2\", // 医生发送的消息\n        sendtime: new Date().toLocaleString()\n      };\n\n      const url = base + \"/chatinfo/add\";\n      request.post(url, param).then((res) => {\n        this.sending = false;\n        this.inputMessage = '';\n        this.$message.success('发送成功');\n\n        // 立即刷新消息列表\n        setTimeout(() => {\n          this.loadMessages();\n        }, 500);\n\n        // 通知父组件消息已发送\n        this.$emit('message-sent');\n      }).catch((error) => {\n        this.sending = false;\n        console.error('发送消息失败:', error);\n        this.$message.error('发送消息失败');\n      });\n    },\n\n    // 滚动到底部\n    scrollToBottom() {\n      const container = this.$refs.chatContainer;\n      if (container) {\n        container.scrollTop = container.scrollHeight;\n      }\n    },\n\n    // 开始自动刷新\n    startAutoRefresh() {\n      this.stopAutoRefresh(); // 先清除之前的定时器\n      this.refreshTimer = setInterval(() => {\n        this.loadMessages();\n      }, 5000); // 每5秒刷新一次\n    },\n\n    // 停止自动刷新\n    stopAutoRefresh() {\n      if (this.refreshTimer) {\n        clearInterval(this.refreshTimer);\n        this.refreshTimer = null;\n      }\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.stopAutoRefresh();\n      this.visible = false;\n      this.inputMessage = '';\n      this.messages = [];\n    }\n  },\n  beforeUnmount() {\n    this.stopAutoRefresh();\n  }\n};\n</script>\n\n<style scoped>\n.chat-dialog {\n  .el-dialog__body {\n    padding: 0;\n  }\n}\n\n.chat-container {\n  height: 400px;\n  overflow-y: auto;\n  padding: 20px;\n  background-color: #f5f5f5;\n}\n\n.chat-messages {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.message-item {\n  display: flex;\n  max-width: 80%;\n}\n\n.patient-message {\n  justify-content: flex-start;\n}\n\n.doctor-message {\n  justify-content: flex-end;\n  align-self: flex-end;\n}\n\n.message-content {\n  background: white;\n  border-radius: 12px;\n  padding: 12px 16px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  position: relative;\n}\n\n.doctor-message .message-content {\n  background: #07c160;\n  color: white;\n}\n\n.patient-message .message-content::before {\n  content: '';\n  position: absolute;\n  left: -8px;\n  top: 12px;\n  width: 0;\n  height: 0;\n  border-top: 8px solid transparent;\n  border-bottom: 8px solid transparent;\n  border-right: 8px solid white;\n}\n\n.doctor-message .message-content::before {\n  content: '';\n  position: absolute;\n  right: -8px;\n  top: 12px;\n  width: 0;\n  height: 0;\n  border-top: 8px solid transparent;\n  border-bottom: 8px solid transparent;\n  border-left: 8px solid #07c160;\n}\n\n.message-text {\n  font-size: 14px;\n  line-height: 1.4;\n  word-wrap: break-word;\n}\n\n.message-time {\n  font-size: 12px;\n  margin-top: 8px;\n  opacity: 0.7;\n}\n\n.doctor-message .message-time {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.patient-message .message-time {\n  color: #999;\n}\n\n.chat-input {\n  padding: 20px;\n  border-top: 1px solid #eee;\n  background: white;\n}\n\n.input-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 10px;\n}\n\n.input-tip {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 滚动条样式 */\n.chat-container::-webkit-scrollbar {\n  width: 6px;\n}\n\n.chat-container::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.chat-container::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.chat-container::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n</style>\n"]}]}