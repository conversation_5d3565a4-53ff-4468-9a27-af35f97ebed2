<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
      <div class="page-header">
        <h3>我收到的聊天</h3>
        <p>管理患者发送的聊天消息，点击回复按钮进行在线沟通</p>
      </div>

<el-table :data="datalist" border stripe style="width: 100%"  v-loading="listLoading"   highlight-current-row   max-height="600"     size="small">
<el-table-column prop="lname" label="患者姓名"  align="center" width="120"></el-table-column>
<el-table-column prop="content" label="最新消息"  align="center" min-width="200">
  <template #default="scope">
    <div class="message-content">
      <span :class="{'unread-message': scope.row.flag == '1'}">{{ scope.row.content }}</span>
      <el-tag v-if="scope.row.flag == '1'" type="danger" size="small" style="margin-left: 8px;">未回复</el-tag>
    </div>
  </template>
</el-table-column>
<el-table-column prop="sendtime" label="发送时间"  align="center" width="160"></el-table-column>
<el-table-column label="操作" min-width="200" align="center">
<template #default="scope">
<el-button type="primary" size="mini" @click="handleReply(scope.$index, scope.row)" icon="el-icon-chat-dot-round" style="padding: 3px 6px 3px 6px;">回复</el-button>
<el-button type="danger" size="mini" @click="handleDelete(scope.$index, scope.row)" icon="el-icon-delete" style=" padding: 3px 6px 3px 6px;">删除</el-button>
</template>
</el-table-column>
</el-table>
<el-pagination  @current-change="handleCurrentChange" :current-page="page.currentPage" :page-size="page.pageSize" 
 background layout="total, prev, pager, next, jumper" :total="page.totalCount"
 style="float: right; margin: 10px 20px 0 0"></el-pagination>

    <!-- 聊天对话框 -->
    <ChatDialog
      v-model="chatDialogVisible"
      :patient-name="selectedPatient.name"
      :patient-id="selectedPatient.id"
      @message-sent="onMessageSent"
    />

    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";
import ChatDialog from "../../../components/ChatDialog.vue";

export default {
  name: 'chatinfo',
  components: {
    ChatDialog
  },
    data() {
      return {
       
        page: {
          currentPage: 1, // 当前页
          pageSize: 10, // 每页显示条目个数
          totalCount: 0, // 总条目数
        },
        isClear: false,      
        
        listLoading: false, //列表加载状态
        btnLoading: false, //保存按钮加载状态
        datalist: [], //表格数据
        chatDialogVisible: false, //聊天对话框显示状态
        selectedPatient: { //选中的患者信息
          name: '',
          id: ''
        },
        refreshTimer: null //自动刷新定时器

      };
    },
    created() {
      this.getDatas();
      this.startAutoRefresh();
    },

    beforeUnmount() {
      this.stopAutoRefresh();
    },

 
    methods: {    

              
       // 删除聊天
        handleDelete(index, row) {
          this.$confirm("确认删除该记录吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              this.listLoading = true;
              let url = base + "/chatinfo/del?id=" + row.cid;
              request.post(url).then((res) => {
                this.listLoading = false;
             
                this.$message({
                  message: "删除成功",
                  type: "success",
                  offset: 320,
                });
                this.getDatas();
              });
            })
            .catch(() => { });
        },
                
        // 分页
        handleCurrentChange(val) {
          this.page.currentPage = val;
          this.getDatas();
        },     
     
        //获取列表数据
      getDatas() {
           var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息
        let para = {
            did:user.did,
            condition:" and a.cid in(select max(cid) from chatinfo where did="+user.did+" group by lname ) "
          };
          this.listLoading = true;
          let url = base + "/chatinfo/list?currentPage=" + this.page.currentPage+ "&pageSize=" + this.page.pageSize;
          request.post(url, para).then((res) => {
            if (res.resdata.length > 0) {
              this.isPage = true;
            } else {
              this.isPage = false;
            }
            this.page.totalCount = res.count;
            this.datalist = res.resdata;
            this.listLoading = false;
          }).catch((error) => {
            console.error('获取聊天信息列表失败:', error);
            this.listLoading = false;
            this.$message.error('获取聊天信息列表失败');
          });
        },
        
           
        // 查看
        handleShow(index, row) {
          this.$router.push({
            path: "/ChatinfoDetail",
             query: {
                id: row.cid,
              },
          });
        },
    
        // 回复聊天
        handleReply(index, row) {
          this.selectedPatient = {
            name: row.lname,
            id: row.lname // 使用用户名作为标识
          };
          this.chatDialogVisible = true;
        },

        // 消息发送成功回调
        onMessageSent() {
          // 刷新聊天列表
          setTimeout(() => {
            this.getDatas();
          }, 1000);
        },

        // 开始自动刷新
        startAutoRefresh() {
          this.stopAutoRefresh(); // 先清除之前的定时器
          this.refreshTimer = setInterval(() => {
            this.getDatas();
          }, 30000); // 每30秒刷新一次列表
        },

        // 停止自动刷新
        stopAutoRefresh() {
          if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
          }
        },

        // 编辑
        handleEdit(index, row) {
          this.$router.push({
            path: "/ChatinfoEdit",
             query: {
                id: row.cid,
              },
          });
        },
      },
}

</script>
<style scoped>
.page-header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-header h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 20px;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.message-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.unread-message {
  font-weight: bold;
  color: #e74c3c;
}

/* 表格样式优化 */
.el-table {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-radius: 8px;
  overflow: hidden;
}

.el-table th {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
}

.el-button {
  border-radius: 4px;
}

.el-button--primary {
  background-color: #007bff;
  border-color: #007bff;
}

.el-button--primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.el-button--danger {
  background-color: #dc3545;
  border-color: #dc3545;
}

.el-button--danger:hover {
  background-color: #c82333;
  border-color: #c82333;
}
</style>
 

