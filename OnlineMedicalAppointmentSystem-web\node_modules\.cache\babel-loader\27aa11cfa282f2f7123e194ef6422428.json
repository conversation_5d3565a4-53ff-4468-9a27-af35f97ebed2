{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\doctor\\DoctorScheduleCalendar.vue?vue&type=template&id=e29a7b4c&scoped=true", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\doctor\\DoctorScheduleCalendar.vue", "mtime": 1749360634991}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "$data", "doctor<PERSON>n<PERSON>", "_hoisted_3", "_toDisplayString", "dname", "pname", "_hoisted_4", "_createCommentVNode", "_hoisted_5", "_Fragment", "_renderList", "dates", "date", "index", "key", "_hoisted_6", "_hoisted_7", "weeks", "_hoisted_8", "timeSlots", "timeSlot", "timeIndex", "_hoisted_9", "weekDay", "dayIndex", "_normalizeClass", "$options", "getScheduleInfo", "hasSchedule", "_hoisted_10", "_hoisted_11", "availableSlots", "_hoisted_12", "totalSlots", "_hoisted_13", "_cache", "loading", "_hoisted_14", "_createVNode", "_component_el_card", "_hoisted_15", "_hoisted_16", "weeklyStats", "scheduledDays", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "bookedSlots", "_hoisted_21", "_hoisted_22"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\doctor\\DoctorScheduleCalendar.vue"], "sourcesContent": ["<template>\n  <div class=\"schedule-calendar\">\n    <div class=\"header\">\n \n      <div class=\"doctor-info\" v-if=\"doctorInfo\">\n        <span>医生：{{ doctorInfo.dname }}</span>\n        <span>科室：{{ doctorInfo.pname }}</span>\n      </div>\n    </div>\n\n    <div class=\"calendar-container\" v-loading=\"loading\">\n      <!-- 日期表头 -->\n      <div class=\"schedule-header\">\n        <div class=\"time-column\">时间段</div>\n        <div class=\"date-column\" v-for=\"(date, index) in dates\" :key=\"index\">\n          <div class=\"date\">{{ date }}</div>\n          <div class=\"week-day\">{{ weeks[index] }}</div>\n        </div>\n      </div>\n\n      <!-- 排班表格 -->\n      <div class=\"schedule-body\">\n        <div class=\"schedule-row\" v-for=\"(timeSlot, timeIndex) in timeSlots\" :key=\"timeIndex\">\n          <!-- 时间段 -->\n          <div class=\"time-cell\">{{ timeSlot }}</div>\n\n          <!-- 每天的排班情况 -->\n          <div \n            class=\"schedule-cell\" \n            v-for=\"(weekDay, dayIndex) in weeks\" \n            :key=\"dayIndex\"\n            :class=\"{ 'has-schedule': getScheduleInfo(timeSlot, weekDay).hasSchedule }\"\n          >\n            <div v-if=\"getScheduleInfo(timeSlot, weekDay).hasSchedule\" class=\"schedule-info\">\n              <div class=\"status available\">可预约</div>\n              <div class=\"remaining\">\n                剩余：{{ getScheduleInfo(timeSlot, weekDay).availableSlots }}\n              </div>\n              <div class=\"total\">\n                总数：{{ getScheduleInfo(timeSlot, weekDay).totalSlots }}\n              </div>\n            </div>\n            <div v-else class=\"no-schedule\">\n              <div class=\"status unavailable\">未排班</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 统计信息 -->\n    <div class=\"statistics\">\n      <el-card class=\"stat-card\">\n        <div class=\"stat-item\">\n          <span class=\"stat-label\">本周排班天数：</span>\n          <span class=\"stat-value\">{{ weeklyStats.scheduledDays }}</span>\n        </div>\n        <div class=\"stat-item\">\n          <span class=\"stat-label\">本周总号数：</span>\n          <span class=\"stat-value\">{{ weeklyStats.totalSlots }}</span>\n        </div>\n        <div class=\"stat-item\">\n          <span class=\"stat-label\">本周已预约：</span>\n          <span class=\"stat-value\">{{ weeklyStats.bookedSlots }}</span>\n        </div>\n        <div class=\"stat-item\">\n          <span class=\"stat-label\">本周剩余：</span>\n          <span class=\"stat-value\">{{ weeklyStats.availableSlots }}</span>\n        </div>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\n\nexport default {\n  name: 'DoctorScheduleCalendar',\n  data() {\n    return {\n      loading: false,\n      doctorInfo: null,\n      dates: [],\n      weeks: [],\n      timeSlots: [\n        \"8:00-9:00\",\n        \"9:00-10:00\", \n        \"10:00-11:00\",\n        \"11:00-12:00\",\n        \"14:00-15:00\",\n        \"15:00-16:00\",\n        \"16:00-17:00\"\n      ],\n      scheduleData: [],\n      weeklyStats: {\n        scheduledDays: 0,\n        totalSlots: 0,\n        bookedSlots: 0,\n        availableSlots: 0\n      }\n    };\n  },\n  created() {\n    this.initDates();\n    this.getDoctorInfo();\n    this.getScheduleData();\n  },\n  methods: {\n    // 初始化未来7天的日期\n    initDates() {\n      const today = new Date();\n      const weekMap = {\n        0: \"星期日\",\n        1: \"星期一\", \n        2: \"星期二\",\n        3: \"星期三\",\n        4: \"星期四\",\n        5: \"星期五\",\n        6: \"星期六\"\n      };\n\n      for (let i = 0; i < 7; i++) {\n        const date = new Date(today);\n        date.setDate(today.getDate() + i);\n        \n        const month = (date.getMonth() + 1).toString().padStart(2, '0');\n        const day = date.getDate().toString().padStart(2, '0');\n        const dateStr = `${month}-${day}`;\n        \n        this.dates.push(dateStr);\n        this.weeks.push(weekMap[date.getDay()]);\n      }\n    },\n\n    // 获取医生信息\n    getDoctorInfo() {\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      if (!user || !user.did) {\n        this.$message.error('请先登录');\n        return;\n      }\n\n      this.loading = true;\n      const url = base + \"/doctor/get?id=\" + user.did;\n      request.post(url).then((res) => {\n        this.doctorInfo = res.resdata;\n        this.loading = false;\n      }).catch((error) => {\n        console.error('获取医生信息失败:', error);\n        this.loading = false;\n        this.$message.error('获取医生信息失败');\n      });\n    },\n\n    // 获取排班数据\n    getScheduleData() {\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      if (!user || !user.did) {\n        return;\n      }\n\n      this.loading = true;\n      const param = {\n        f: 5,\n        did: user.did,\n        loadmsg: '正在加载中'\n      };\n\n      const url = base + \"/plans/list3?currentPage=1&pageSize=500\";\n      request.post(url, param).then((res) => {\n        if (res.resdata && res.resdata.length > 0) {\n          this.scheduleData = res.resdata;\n          this.calculateWeeklyStats();\n        }\n        this.loading = false;\n      }).catch((error) => {\n        console.error('获取排班数据失败:', error);\n        this.loading = false;\n        this.$message.error('获取排班数据失败');\n      });\n    },\n\n    // 获取指定时间段和星期的排班信息\n    getScheduleInfo(timeSlot, weekDay) {\n      const schedule = this.scheduleData.find(\n        item => item.ptime === timeSlot && item.weeks === weekDay\n      );\n\n      if (schedule) {\n        const totalSlots = parseInt(schedule.people) || 0;\n        const bookedSlots = parseInt(schedule.by1) || 0;\n        const availableSlots = totalSlots - bookedSlots;\n\n        return {\n          hasSchedule: true,\n          totalSlots: totalSlots,\n          bookedSlots: bookedSlots,\n          availableSlots: availableSlots > 0 ? availableSlots : 0\n        };\n      }\n\n      return {\n        hasSchedule: false,\n        totalSlots: 0,\n        bookedSlots: 0,\n        availableSlots: 0\n      };\n    },\n\n    // 计算本周统计数据\n    calculateWeeklyStats() {\n      let scheduledDays = new Set();\n      let totalSlots = 0;\n      let bookedSlots = 0;\n\n      this.scheduleData.forEach(schedule => {\n        if (this.weeks.includes(schedule.weeks)) {\n          scheduledDays.add(schedule.weeks);\n          totalSlots += parseInt(schedule.people) || 0;\n          bookedSlots += parseInt(schedule.by1) || 0;\n        }\n      });\n\n      this.weeklyStats = {\n        scheduledDays: scheduledDays.size,\n        totalSlots: totalSlots,\n        bookedSlots: bookedSlots,\n        availableSlots: totalSlots - bookedSlots\n      };\n    }\n  }\n};\n</script>\n\n<style scoped>\n.schedule-calendar {\n  padding: 20px;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header h2 {\n  margin: 0 0 10px 0;\n  color: #333;\n  font-size: 24px;\n}\n\n.doctor-info {\n  color: #666;\n  font-size: 14px;\n}\n\n.doctor-info span {\n  margin-right: 20px;\n}\n\n.calendar-container {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.schedule-header {\n  display: flex;\n  background-color: #f8f9fa;\n  border-bottom: 2px solid #dee2e6;\n}\n\n.time-column {\n  width: 120px;\n  padding: 15px 10px;\n  text-align: center;\n  font-weight: bold;\n  background-color: #e9ecef;\n  border-right: 1px solid #dee2e6;\n}\n\n.date-column {\n  flex: 1;\n  padding: 10px;\n  text-align: center;\n  border-right: 1px solid #dee2e6;\n  min-width: 100px;\n}\n\n.date {\n  font-weight: bold;\n  font-size: 16px;\n  color: #333;\n}\n\n.week-day {\n  font-size: 12px;\n  color: #666;\n  margin-top: 4px;\n}\n\n.schedule-body {\n  border-bottom: 1px solid #dee2e6;\n}\n\n.schedule-row {\n  display: flex;\n  border-bottom: 1px solid #dee2e6;\n}\n\n.schedule-row:last-child {\n  border-bottom: none;\n}\n\n.time-cell {\n  width: 120px;\n  padding: 20px 10px;\n  text-align: center;\n  background-color: #f8f9fa;\n  border-right: 1px solid #dee2e6;\n  font-weight: 500;\n  color: #495057;\n}\n\n.schedule-cell {\n  flex: 1;\n  padding: 15px 10px;\n  text-align: center;\n  border-right: 1px solid #dee2e6;\n  min-height: 80px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 100px;\n}\n\n.schedule-cell.has-schedule {\n  background-color: #e8f5e8;\n}\n\n.schedule-info {\n  width: 100%;\n}\n\n.status.available {\n  color: #28a745;\n  font-weight: bold;\n  margin-bottom: 5px;\n}\n\n.status.unavailable {\n  color: #6c757d;\n  font-style: italic;\n}\n\n.remaining, .total {\n  font-size: 12px;\n  color: #666;\n  margin: 2px 0;\n}\n\n.no-schedule {\n  width: 100%;\n  color: #999;\n}\n\n.statistics {\n  margin-top: 20px;\n}\n\n.stat-card {\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.stat-item {\n  display: inline-block;\n  margin-right: 30px;\n  margin-bottom: 10px;\n}\n\n.stat-label {\n  color: #666;\n  font-size: 14px;\n}\n\n.stat-value {\n  color: #333;\n  font-weight: bold;\n  font-size: 16px;\n  margin-left: 5px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .schedule-header,\n  .schedule-row {\n    min-width: 800px;\n  }\n  \n  .calendar-container {\n    overflow-x: auto;\n  }\n  \n  .stat-item {\n    display: block;\n    margin-bottom: 15px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAQ;;;EAEZA,KAAK,EAAC;;;EAMRA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAiB;;EAGnBA,KAAK,EAAC;AAAM;;EACZA,KAAK,EAAC;AAAU;;EAKpBA,KAAK,EAAC;AAAe;;EAGjBA,KAAK,EAAC;AAAW;;;EASuCA,KAAK,EAAC;;;EAE1DA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAO;;;EAIRA,KAAK,EAAC;;;EASrBA,KAAK,EAAC;AAAY;;EAEdA,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAY;;EAErBA,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAY;;EAErBA,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAY;;EAErBA,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAY;;;;uBAlEhCC,mBAAA,CAsEM,OAtENC,UAsEM,GArEJC,mBAAA,CAMM,OANNC,UAMM,GAJ2BC,KAAA,CAAAC,UAAU,I,cAAzCL,mBAAA,CAGM,OAHNM,UAGM,GAFJJ,mBAAA,CAAsC,cAAhC,KAAG,GAAAK,gBAAA,CAAGH,KAAA,CAAAC,UAAU,CAACG,KAAK,kBAC5BN,mBAAA,CAAsC,cAAhC,KAAG,GAAAK,gBAAA,CAAGH,KAAA,CAAAC,UAAU,CAACI,KAAK,iB,yEAIhCT,mBAAA,CAsCM,OAtCNU,UAsCM,GArCJC,mBAAA,UAAa,EACbT,mBAAA,CAMM,OANNU,UAMM,G,0BALJV,mBAAA,CAAkC;IAA7BH,KAAK,EAAC;EAAa,GAAC,KAAG,uB,kBAC5BC,mBAAA,CAGMa,SAAA,QAAAC,WAAA,CAH2CV,KAAA,CAAAW,KAAK,GAArBC,IAAI,EAAEC,KAAK;yBAA5CjB,mBAAA,CAGM;MAHDD,KAAK,EAAC,aAAa;MAAiCmB,GAAG,EAAED;QAC5Df,mBAAA,CAAkC,OAAlCiB,UAAkC,EAAAZ,gBAAA,CAAbS,IAAI,kBACzBd,mBAAA,CAA8C,OAA9CkB,UAA8C,EAAAb,gBAAA,CAArBH,KAAA,CAAAiB,KAAK,CAACJ,KAAK,kB;oCAIxCN,mBAAA,UAAa,EACbT,mBAAA,CA0BM,OA1BNoB,UA0BM,I,kBAzBJtB,mBAAA,CAwBMa,SAAA,QAAAC,WAAA,CAxBoDV,KAAA,CAAAmB,SAAS,GAAjCC,QAAQ,EAAEC,SAAS;yBAArDzB,mBAAA,CAwBM;MAxBDD,KAAK,EAAC,cAAc;MAA6CmB,GAAG,EAAEO;QACzEd,mBAAA,SAAY,EACZT,mBAAA,CAA2C,OAA3CwB,UAA2C,EAAAnB,gBAAA,CAAjBiB,QAAQ,kBAElCb,mBAAA,aAAgB,G,kBAChBX,mBAAA,CAkBMa,SAAA,QAAAC,WAAA,CAhB0BV,KAAA,CAAAiB,KAAK,GAA3BM,OAAO,EAAEC,QAAQ;2BAF3B5B,mBAAA,CAkBM;QAjBJD,KAAK,EAAA8B,eAAA,EAAC,eAAe;UAAA,gBAGKC,QAAA,CAAAC,eAAe,CAACP,QAAQ,EAAEG,OAAO,EAAEK;QAAW;QADvEd,GAAG,EAAEU;UAGKE,QAAA,CAAAC,eAAe,CAACP,QAAQ,EAAEG,OAAO,EAAEK,WAAW,I,cAAzDhC,mBAAA,CAQM,OARNiC,WAQM,G,0BAPJ/B,mBAAA,CAAuC;QAAlCH,KAAK,EAAC;MAAkB,GAAC,KAAG,sBACjCG,mBAAA,CAEM,OAFNgC,WAEM,EAFiB,MAClB,GAAA3B,gBAAA,CAAGuB,QAAA,CAAAC,eAAe,CAACP,QAAQ,EAAEG,OAAO,EAAEQ,cAAc,kBAEzDjC,mBAAA,CAEM,OAFNkC,WAEM,EAFa,MACd,GAAA7B,gBAAA,CAAGuB,QAAA,CAAAC,eAAe,CAACP,QAAQ,EAAEG,OAAO,EAAEU,UAAU,iB,oBAGvDrC,mBAAA,CAEM,OAFNsC,WAEM,OAAAC,MAAA,QAAAA,MAAA,OADJrC,mBAAA,CAAyC;QAApCH,KAAK,EAAC;MAAoB,GAAC,KAAG,oB;;6DAjCFK,KAAA,CAAAoC,OAAO,E,GAwClD7B,mBAAA,UAAa,EACbT,mBAAA,CAmBM,OAnBNuC,WAmBM,GAlBJC,YAAA,CAiBUC,kBAAA;IAjBD5C,KAAK,EAAC;EAAW;sBACxB,MAGM,CAHNG,mBAAA,CAGM,OAHN0C,WAGM,G,0BAFJ1C,mBAAA,CAAuC;MAAjCH,KAAK,EAAC;IAAY,GAAC,SAAO,sBAChCG,mBAAA,CAA+D,QAA/D2C,WAA+D,EAAAtC,gBAAA,CAAnCH,KAAA,CAAA0C,WAAW,CAACC,aAAa,iB,GAEvD7C,mBAAA,CAGM,OAHN8C,WAGM,G,0BAFJ9C,mBAAA,CAAsC;MAAhCH,KAAK,EAAC;IAAY,GAAC,QAAM,sBAC/BG,mBAAA,CAA4D,QAA5D+C,WAA4D,EAAA1C,gBAAA,CAAhCH,KAAA,CAAA0C,WAAW,CAACT,UAAU,iB,GAEpDnC,mBAAA,CAGM,OAHNgD,WAGM,G,0BAFJhD,mBAAA,CAAsC;MAAhCH,KAAK,EAAC;IAAY,GAAC,QAAM,sBAC/BG,mBAAA,CAA6D,QAA7DiD,WAA6D,EAAA5C,gBAAA,CAAjCH,KAAA,CAAA0C,WAAW,CAACM,WAAW,iB,GAErDlD,mBAAA,CAGM,OAHNmD,WAGM,G,0BAFJnD,mBAAA,CAAqC;MAA/BH,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC9BG,mBAAA,CAAgE,QAAhEoD,WAAgE,EAAA/C,gBAAA,CAApCH,KAAA,CAAA0C,WAAW,CAACX,cAAc,iB", "ignoreList": []}]}