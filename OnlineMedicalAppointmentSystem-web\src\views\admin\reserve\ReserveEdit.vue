﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules"  align="left">
<el-form-item label="预约ID">
{{formData.rid}}</el-form-item>
<el-form-item label="科室">
{{formData.pname}}</el-form-item>
<el-form-item label="医生">
{{formData.by1}}</el-form-item>
<el-form-item label="坐诊ID">
{{formData.plid}}</el-form-item>
<el-form-item label="预约日期">
{{formData.rdate}}</el-form-item>
<el-form-item label="预约时间段">
{{formData.rtime}}</el-form-item>
<el-form-item label="挂号费">
{{formData.pmoney}}</el-form-item>
<el-form-item label="用户名">
{{formData.lname}}</el-form-item>

<el-form-item label="就诊人">
{{patient.peoname}}
</el-form-item>
<el-form-item label="手机号码">
{{patient.phone}}
</el-form-item>
<el-form-item label="性别">
{{patient.gender}}
</el-form-item>
<el-form-item label="年龄">
{{patient.age}}
</el-form-item>


<el-form-item label="提交时间">
{{formData.addtime}}</el-form-item>
<el-form-item label="预约状态">
{{formData.flag}}</el-form-item>
<el-form-item label="诊断结果" prop="results">
<WangEditor  ref="wangEditorRef" v-model="formData.results" :config="editorConfig"   :isClear="isClear" @change="editorChange"></WangEditor>
</el-form-item>
<el-form-item>
<el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
<el-button type="info" size="small" @click="goBack" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
import request, { base } from "../../../../utils/http";
import WangEditor from "../../../components/WangEditor";
export default {
  name: 'ReserveEdit',
  components: {
    WangEditor,
  },  
    data() {
      return {   
        id: '',
        isClear: false,
        uploadVisible: false, 
        btnLoading: false, //保存按钮加载状态     
        formData: {}, //表单数据           
                     patient: {}, // 初始化患者对象
        
      };
    },
    created() {
    this.id = this.$route.query.id;
      this.getDatas();
      this.getpartsList();
    },

 
    methods: {    

//获取列表数据
        getDatas() {
          let para = {
          };
          this.listLoading = true;
          let url = base + "/reserve/get?id=" + this.id;
          request.post(url, para).then((res) => {
            this.formData = JSON.parse(JSON.stringify(res.resdata));
            this.listLoading = false;
            this.$refs["wangEditorRef"].editor.txt.html(this.formData.results);
        

              let param = {
                            peoid: this.formData.peoid,
                        };
                        let url2 = base + "/patient/get?id=" + this.formData.peoid;
                        request.post(url2, param).then((res) => {
                            this.patient = JSON.parse(JSON.stringify(res.resdata));
                        });

          });
        },
    
        // 添加
        save() {
          this.$refs["formDataRef"].validate((valid) => { //验证表单
            if (valid) {
              let url = base + "/reserve/update";
              this.btnLoading = true;

              this.formData.flag='已就诊'

              request.post(url, this.formData).then((res) => { //发送请求         
                if (res.code == 200) {
                  this.$message({
                    message: "操作成功",
                    type: "success",
                    offset: 320,
                  });
                  this.$router.push({
                    path: "/ReserveManage2",
                  });
                } else {
                  this.$message({
                    message:res.msg,
                    type: "error",
                    offset: 320,
                  });
                }
                this.btnLoading = false;
              });
            }
    
          });
        },
        
       // 返回
        goBack() {
          this.$router.push({
            path: "/ReserveManage2",
          });
        },       
              
            
    getpartsList() {
      let para = {};
      this.listLoading = true;
      let url = base + "/parts/list?currentPage=1&pageSize=1000";
      request.post(url, para).then((res) => {
        this.partsList = res.resdata;
      });
    },
  
           
            // 富文本编辑器
    editorChange(val) {
      this.formData.results = val;
    },
   
      },
}

</script>
<style scoped>
</style>
 

