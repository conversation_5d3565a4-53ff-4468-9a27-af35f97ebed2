package com.controller;

import com.model.*;
import com.response.Response;
import com.service.*;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/chatinfo")
public class ChatinfoController{
	
	@Resource
	private ChatinfoService chatinfoService;
	
	//聊天列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<Chatinfo>> list(@RequestBody Chatinfo chatinfo, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = chatinfoService.getCount(chatinfo);
		//获取当前页记录
		List<Chatinfo> chatinfoList = chatinfoService.queryChatinfoList(chatinfo, page);
        
		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(chatinfoList, counts, page_count);
	}

		
	//聊天列表
	@RequestMapping(value="/list2")
	@CrossOrigin
	public Response<List<Chatinfo>> list2( Chatinfo chatinfo, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = chatinfoService.getCount(chatinfo);
		//获取当前页记录
		List<Chatinfo> chatinfoList = chatinfoService.queryChatinfoList(chatinfo, page);
        
		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(chatinfoList, counts, page_count);
	}
        
        
	//添加聊天
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add(@RequestBody Chatinfo chatinfo, HttpServletRequest req) throws Exception {
		try {
			//处理中文编码问题
			if (chatinfo.getContent() != null) {
				chatinfo.setContent(new String(chatinfo.getContent().getBytes("ISO-8859-1"), "UTF-8"));
			}
			if (chatinfo.getLname() != null) {
				chatinfo.setLname(new String(chatinfo.getLname().getBytes("ISO-8859-1"), "UTF-8"));
			}

			chatinfoService.insertChatinfo(chatinfo); //添加

			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}

		//添加聊天
	@ResponseBody
	@PostMapping(value = "/add2")
	@CrossOrigin
	public Response add2( Chatinfo chatinfo, HttpServletRequest req) throws Exception {
		
		try {
			//姓名转为utf-8
			chatinfo.setContent(new String(chatinfo.getContent().getBytes("ISO-8859-1"), "UTF-8"));

			chatinfoService.insertChatinfo(chatinfo); //添加
   
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
    
	//删除聊天
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			chatinfoService.deleteChatinfo(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改聊天
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Chatinfo chatinfo, HttpServletRequest req) throws Exception {
		try {
			chatinfoService.updateChatinfo(chatinfo); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回聊天详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			Chatinfo chatinfo=chatinfoService.queryChatinfoById(id); //根据ID查询
			return Response.success(chatinfo);
			} catch (Exception e) {
			return Response.error();
		}
       
	}
    
}

