{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\doctor\\DoctorScheduleCalendar.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\doctor\\DoctorScheduleCalendar.vue", "mtime": 1749360634991}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\doctor\\DoctorScheduleCalendar.vue"], "names": [], "mappings": ";AA2EA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB;IACF,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC;;MAED,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;QAEjC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC;IACF,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACZ,CAAC,EAAE,CAAC;QACJ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACxD,CAAC;MACH;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC;IACH,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAClB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC5C;MACF,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC;IACH;EACF;AACF,CAAC", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/admin/doctor/DoctorScheduleCalendar.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"schedule-calendar\">\n    <div class=\"header\">\n \n      <div class=\"doctor-info\" v-if=\"doctorInfo\">\n        <span>医生：{{ doctorInfo.dname }}</span>\n        <span>科室：{{ doctorInfo.pname }}</span>\n      </div>\n    </div>\n\n    <div class=\"calendar-container\" v-loading=\"loading\">\n      <!-- 日期表头 -->\n      <div class=\"schedule-header\">\n        <div class=\"time-column\">时间段</div>\n        <div class=\"date-column\" v-for=\"(date, index) in dates\" :key=\"index\">\n          <div class=\"date\">{{ date }}</div>\n          <div class=\"week-day\">{{ weeks[index] }}</div>\n        </div>\n      </div>\n\n      <!-- 排班表格 -->\n      <div class=\"schedule-body\">\n        <div class=\"schedule-row\" v-for=\"(timeSlot, timeIndex) in timeSlots\" :key=\"timeIndex\">\n          <!-- 时间段 -->\n          <div class=\"time-cell\">{{ timeSlot }}</div>\n\n          <!-- 每天的排班情况 -->\n          <div \n            class=\"schedule-cell\" \n            v-for=\"(weekDay, dayIndex) in weeks\" \n            :key=\"dayIndex\"\n            :class=\"{ 'has-schedule': getScheduleInfo(timeSlot, weekDay).hasSchedule }\"\n          >\n            <div v-if=\"getScheduleInfo(timeSlot, weekDay).hasSchedule\" class=\"schedule-info\">\n              <div class=\"status available\">可预约</div>\n              <div class=\"remaining\">\n                剩余：{{ getScheduleInfo(timeSlot, weekDay).availableSlots }}\n              </div>\n              <div class=\"total\">\n                总数：{{ getScheduleInfo(timeSlot, weekDay).totalSlots }}\n              </div>\n            </div>\n            <div v-else class=\"no-schedule\">\n              <div class=\"status unavailable\">未排班</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 统计信息 -->\n    <div class=\"statistics\">\n      <el-card class=\"stat-card\">\n        <div class=\"stat-item\">\n          <span class=\"stat-label\">本周排班天数：</span>\n          <span class=\"stat-value\">{{ weeklyStats.scheduledDays }}</span>\n        </div>\n        <div class=\"stat-item\">\n          <span class=\"stat-label\">本周总号数：</span>\n          <span class=\"stat-value\">{{ weeklyStats.totalSlots }}</span>\n        </div>\n        <div class=\"stat-item\">\n          <span class=\"stat-label\">本周已预约：</span>\n          <span class=\"stat-value\">{{ weeklyStats.bookedSlots }}</span>\n        </div>\n        <div class=\"stat-item\">\n          <span class=\"stat-label\">本周剩余：</span>\n          <span class=\"stat-value\">{{ weeklyStats.availableSlots }}</span>\n        </div>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\n\nexport default {\n  name: 'DoctorScheduleCalendar',\n  data() {\n    return {\n      loading: false,\n      doctorInfo: null,\n      dates: [],\n      weeks: [],\n      timeSlots: [\n        \"8:00-9:00\",\n        \"9:00-10:00\", \n        \"10:00-11:00\",\n        \"11:00-12:00\",\n        \"14:00-15:00\",\n        \"15:00-16:00\",\n        \"16:00-17:00\"\n      ],\n      scheduleData: [],\n      weeklyStats: {\n        scheduledDays: 0,\n        totalSlots: 0,\n        bookedSlots: 0,\n        availableSlots: 0\n      }\n    };\n  },\n  created() {\n    this.initDates();\n    this.getDoctorInfo();\n    this.getScheduleData();\n  },\n  methods: {\n    // 初始化未来7天的日期\n    initDates() {\n      const today = new Date();\n      const weekMap = {\n        0: \"星期日\",\n        1: \"星期一\", \n        2: \"星期二\",\n        3: \"星期三\",\n        4: \"星期四\",\n        5: \"星期五\",\n        6: \"星期六\"\n      };\n\n      for (let i = 0; i < 7; i++) {\n        const date = new Date(today);\n        date.setDate(today.getDate() + i);\n        \n        const month = (date.getMonth() + 1).toString().padStart(2, '0');\n        const day = date.getDate().toString().padStart(2, '0');\n        const dateStr = `${month}-${day}`;\n        \n        this.dates.push(dateStr);\n        this.weeks.push(weekMap[date.getDay()]);\n      }\n    },\n\n    // 获取医生信息\n    getDoctorInfo() {\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      if (!user || !user.did) {\n        this.$message.error('请先登录');\n        return;\n      }\n\n      this.loading = true;\n      const url = base + \"/doctor/get?id=\" + user.did;\n      request.post(url).then((res) => {\n        this.doctorInfo = res.resdata;\n        this.loading = false;\n      }).catch((error) => {\n        console.error('获取医生信息失败:', error);\n        this.loading = false;\n        this.$message.error('获取医生信息失败');\n      });\n    },\n\n    // 获取排班数据\n    getScheduleData() {\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      if (!user || !user.did) {\n        return;\n      }\n\n      this.loading = true;\n      const param = {\n        f: 5,\n        did: user.did,\n        loadmsg: '正在加载中'\n      };\n\n      const url = base + \"/plans/list3?currentPage=1&pageSize=500\";\n      request.post(url, param).then((res) => {\n        if (res.resdata && res.resdata.length > 0) {\n          this.scheduleData = res.resdata;\n          this.calculateWeeklyStats();\n        }\n        this.loading = false;\n      }).catch((error) => {\n        console.error('获取排班数据失败:', error);\n        this.loading = false;\n        this.$message.error('获取排班数据失败');\n      });\n    },\n\n    // 获取指定时间段和星期的排班信息\n    getScheduleInfo(timeSlot, weekDay) {\n      const schedule = this.scheduleData.find(\n        item => item.ptime === timeSlot && item.weeks === weekDay\n      );\n\n      if (schedule) {\n        const totalSlots = parseInt(schedule.people) || 0;\n        const bookedSlots = parseInt(schedule.by1) || 0;\n        const availableSlots = totalSlots - bookedSlots;\n\n        return {\n          hasSchedule: true,\n          totalSlots: totalSlots,\n          bookedSlots: bookedSlots,\n          availableSlots: availableSlots > 0 ? availableSlots : 0\n        };\n      }\n\n      return {\n        hasSchedule: false,\n        totalSlots: 0,\n        bookedSlots: 0,\n        availableSlots: 0\n      };\n    },\n\n    // 计算本周统计数据\n    calculateWeeklyStats() {\n      let scheduledDays = new Set();\n      let totalSlots = 0;\n      let bookedSlots = 0;\n\n      this.scheduleData.forEach(schedule => {\n        if (this.weeks.includes(schedule.weeks)) {\n          scheduledDays.add(schedule.weeks);\n          totalSlots += parseInt(schedule.people) || 0;\n          bookedSlots += parseInt(schedule.by1) || 0;\n        }\n      });\n\n      this.weeklyStats = {\n        scheduledDays: scheduledDays.size,\n        totalSlots: totalSlots,\n        bookedSlots: bookedSlots,\n        availableSlots: totalSlots - bookedSlots\n      };\n    }\n  }\n};\n</script>\n\n<style scoped>\n.schedule-calendar {\n  padding: 20px;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header h2 {\n  margin: 0 0 10px 0;\n  color: #333;\n  font-size: 24px;\n}\n\n.doctor-info {\n  color: #666;\n  font-size: 14px;\n}\n\n.doctor-info span {\n  margin-right: 20px;\n}\n\n.calendar-container {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.schedule-header {\n  display: flex;\n  background-color: #f8f9fa;\n  border-bottom: 2px solid #dee2e6;\n}\n\n.time-column {\n  width: 120px;\n  padding: 15px 10px;\n  text-align: center;\n  font-weight: bold;\n  background-color: #e9ecef;\n  border-right: 1px solid #dee2e6;\n}\n\n.date-column {\n  flex: 1;\n  padding: 10px;\n  text-align: center;\n  border-right: 1px solid #dee2e6;\n  min-width: 100px;\n}\n\n.date {\n  font-weight: bold;\n  font-size: 16px;\n  color: #333;\n}\n\n.week-day {\n  font-size: 12px;\n  color: #666;\n  margin-top: 4px;\n}\n\n.schedule-body {\n  border-bottom: 1px solid #dee2e6;\n}\n\n.schedule-row {\n  display: flex;\n  border-bottom: 1px solid #dee2e6;\n}\n\n.schedule-row:last-child {\n  border-bottom: none;\n}\n\n.time-cell {\n  width: 120px;\n  padding: 20px 10px;\n  text-align: center;\n  background-color: #f8f9fa;\n  border-right: 1px solid #dee2e6;\n  font-weight: 500;\n  color: #495057;\n}\n\n.schedule-cell {\n  flex: 1;\n  padding: 15px 10px;\n  text-align: center;\n  border-right: 1px solid #dee2e6;\n  min-height: 80px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 100px;\n}\n\n.schedule-cell.has-schedule {\n  background-color: #e8f5e8;\n}\n\n.schedule-info {\n  width: 100%;\n}\n\n.status.available {\n  color: #28a745;\n  font-weight: bold;\n  margin-bottom: 5px;\n}\n\n.status.unavailable {\n  color: #6c757d;\n  font-style: italic;\n}\n\n.remaining, .total {\n  font-size: 12px;\n  color: #666;\n  margin: 2px 0;\n}\n\n.no-schedule {\n  width: 100%;\n  color: #999;\n}\n\n.statistics {\n  margin-top: 20px;\n}\n\n.stat-card {\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.stat-item {\n  display: inline-block;\n  margin-right: 30px;\n  margin-bottom: 10px;\n}\n\n.stat-label {\n  color: #666;\n  font-size: 14px;\n}\n\n.stat-value {\n  color: #333;\n  font-weight: bold;\n  font-size: 16px;\n  margin-left: 5px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .schedule-header,\n  .schedule-row {\n    min-width: 800px;\n  }\n  \n  .calendar-container {\n    overflow-x: auto;\n  }\n  \n  .stat-item {\n    display: block;\n    margin-bottom: 15px;\n  }\n}\n</style>\n"]}]}