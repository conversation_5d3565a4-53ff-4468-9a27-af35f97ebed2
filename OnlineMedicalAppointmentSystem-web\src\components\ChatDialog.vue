<template>
  <el-dialog
    v-model="visible"
    :title="`与${patientName}的对话`"
    width="700px"
    :before-close="handleClose"
    class="chat-dialog"
    top="5vh"
  >
    <!-- 聊天头部信息 -->
    <div class="chat-header">
      <div class="patient-info">
        <div class="avatar">
          <i class="el-icon-user-solid"></i>
        </div>
        <div class="info">
          <div class="name">{{ patientName }}</div>
          <div class="status">在线咨询</div>
        </div>
      </div>
      <div class="chat-actions">
        <el-button size="small" type="text" @click="loadMessages">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
    </div>

    <!-- 聊天消息区域 -->
    <div class="chat-container" ref="chatContainer">
      <div class="chat-messages">
        <div v-if="messages.length === 0" class="empty-messages">
          <i class="el-icon-chat-dot-round"></i>
          <p>暂无聊天记录</p>
        </div>
        <div
          v-for="(message, index) in messages"
          :key="index"
          :class="['message-item', message.flag == '2' ? 'doctor-message' : 'patient-message']"
        >
          <div class="avatar-wrapper">
            <div class="message-avatar" :class="message.flag == '2' ? 'doctor-avatar' : 'patient-avatar'">
              <i :class="message.flag == '2' ? 'el-icon-s-custom' : 'el-icon-user-solid'"></i>
            </div>
          </div>
          <div class="message-wrapper">
            <div class="message-content">
              <div class="message-text">{{ message.content }}</div>
            </div>
            <div class="message-time">{{ formatTime(message.sendtime) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input">
      <div class="input-wrapper">
        <el-input
          v-model="inputMessage"
          type="textarea"
          :rows="3"
          placeholder="请输入回复内容..."
          maxlength="500"
          show-word-limit
          @keyup.ctrl.enter="sendMessage"
          class="message-input"
        ></el-input>
        <div class="input-actions">
          <div class="input-tips">
            <span class="tip-item">
              <i class="el-icon-info"></i>
              Ctrl + Enter 快速发送
            </span>
            <span class="tip-item">
              <i class="el-icon-time"></i>
              每5秒自动刷新
            </span>
          </div>
          <div class="send-actions">
            <el-button @click="inputMessage = ''" size="small">清空</el-button>
            <el-button
              type="primary"
              @click="sendMessage"
              :loading="sending"
              :disabled="!inputMessage.trim()"
              class="send-btn"
            >
              <i class="el-icon-s-promotion"></i>
              发送
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import request, { base } from "../../utils/http";

export default {
  name: 'ChatDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    patientName: {
      type: String,
      default: ''
    },
    patientId: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue', 'message-sent'],
  data() {
    return {
      messages: [],
      inputMessage: '',
      sending: false,
      refreshTimer: null
    };
  },
  computed: {
    visible: {
      get() {
        return this.modelValue;
      },
      set(value) {
        this.$emit('update:modelValue', value);
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadMessages();
        this.startAutoRefresh();
      } else {
        this.stopAutoRefresh();
      }
    }
  },
  methods: {
    // 加载聊天消息
    loadMessages() {
      if (!this.patientId) return;

      const user = JSON.parse(sessionStorage.getItem("user"));
      const param = {
        lname: this.patientName,
        did: user.did
      };

      const url = base + "/chatinfo/list2?currentPage=1&pageSize=5000";
      request.post(url, param).then((res) => {
        if (res.resdata) {
          this.messages = res.resdata.sort((a, b) => {
            return new Date(a.sendtime) - new Date(b.sendtime);
          });
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        }
      }).catch((error) => {
        console.error('加载聊天消息失败:', error);
        this.$message.error('加载聊天消息失败');
      });
    },

    // 发送消息
    sendMessage() {
      if (!this.inputMessage.trim()) {
        this.$message.warning('请输入消息内容');
        return;
      }

      const user = JSON.parse(sessionStorage.getItem("user"));
      this.sending = true;

      const param = {
        lname: this.patientName,
        did: user.did,
        content: this.inputMessage,
        flag: "2", // 医生发送的消息
        sendtime: new Date().toLocaleString()
      };

      const url = base + "/chatinfo/add";
      request.post(url, param).then((res) => {
        this.sending = false;
        this.inputMessage = '';
        this.$message.success('发送成功');

        // 立即刷新消息列表
        setTimeout(() => {
          this.loadMessages();
        }, 500);

        // 通知父组件消息已发送
        this.$emit('message-sent');
      }).catch((error) => {
        this.sending = false;
        console.error('发送消息失败:', error);
        this.$message.error('发送消息失败');
      });
    },

    // 滚动到底部
    scrollToBottom() {
      const container = this.$refs.chatContainer;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    },

    // 开始自动刷新
    startAutoRefresh() {
      this.stopAutoRefresh(); // 先清除之前的定时器
      this.refreshTimer = setInterval(() => {
        this.loadMessages();
      }, 5000); // 每5秒刷新一次
    },

    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '';
      try {
        const date = new Date(timeStr);
        const now = new Date();
        const diff = now - date;

        // 如果是今天
        if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
          return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
          });
        }

        // 如果是昨天
        const yesterday = new Date(now);
        yesterday.setDate(now.getDate() - 1);
        if (date.getDate() === yesterday.getDate()) {
          return '昨天 ' + date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
          });
        }

        // 其他日期
        return date.toLocaleDateString('zh-CN') + ' ' +
               date.toLocaleTimeString('zh-CN', {
                 hour: '2-digit',
                 minute: '2-digit'
               });
      } catch (e) {
        return timeStr;
      }
    },

    // 关闭对话框
    handleClose() {
      this.stopAutoRefresh();
      this.visible = false;
      this.inputMessage = '';
      this.messages = [];
    }
  },
  beforeUnmount() {
    this.stopAutoRefresh();
  }
};
</script>

<style scoped>
/* 对话框整体样式 */
.chat-dialog :deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

.chat-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  margin: 0;
}

.chat-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.chat-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 20px;
}

.chat-dialog :deep(.el-dialog__body) {
  padding: 0;
  background: #f8f9fa;
}

/* 聊天头部 */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.patient-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.patient-info .avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.patient-info .info .name {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.patient-info .info .status {
  font-size: 12px;
  color: #28a745;
  margin-top: 2px;
}

.chat-actions .el-button {
  color: #6c757d;
}

/* 聊天容器 */
.chat-container {
  height: 450px;
  overflow-y: auto;
  padding: 20px 24px;
  background: #f8f9fa;
}

.empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6c757d;
}

.empty-messages i {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.empty-messages p {
  margin: 0;
  font-size: 14px;
}

/* 消息列表 */
.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  max-width: 85%;
}

.patient-message {
  align-self: flex-start;
}

.doctor-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

/* 头像 */
.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.patient-avatar {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.doctor-avatar {
  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
}

/* 消息内容 */
.message-wrapper {
  flex: 1;
  min-width: 0;
}

.message-content {
  border-radius: 18px;
  padding: 12px 16px;
  position: relative;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.patient-message .message-content {
  background: white;
  color: #333;
  border-bottom-left-radius: 6px;
}

.doctor-message .message-content {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  color: white;
  border-bottom-right-radius: 6px;
}

.message-text {
  font-size: 14px;
  line-height: 1.5;
  word-break: break-word;
}

.message-time {
  font-size: 11px;
  margin-top: 4px;
  opacity: 0.7;
  text-align: center;
}

.doctor-message .message-time {
  text-align: right;
}

.patient-message .message-time {
  text-align: left;
}

/* 输入区域 */
.chat-input {
  background: white;
  border-top: 1px solid #e9ecef;
  padding: 0;
}

.input-wrapper {
  padding: 20px 24px;
}

.message-input :deep(.el-textarea__inner) {
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  transition: all 0.3s ease;
}

.message-input :deep(.el-textarea__inner):focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.input-tips {
  display: flex;
  gap: 16px;
}

.tip-item {
  font-size: 12px;
  color: #6c757d;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tip-item i {
  font-size: 14px;
}

.send-actions {
  display: flex;
  gap: 8px;
}

.send-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 20px;
  padding: 8px 20px;
  font-weight: 600;
}

.send-btn:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.send-btn:disabled {
  background: #e9ecef;
  color: #6c757d;
  transform: none;
  box-shadow: none;
}

/* 滚动条样式 */
.chat-container::-webkit-scrollbar {
  width: 6px;
}

.chat-container::-webkit-scrollbar-track {
  background: transparent;
}

.chat-container::-webkit-scrollbar-thumb {
  background: rgba(0,0,0,0.2);
  border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0,0,0,0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }

  .chat-container {
    height: 350px;
    padding: 16px;
  }

  .input-wrapper {
    padding: 16px;
  }

  .input-tips {
    flex-direction: column;
    gap: 8px;
  }

  .input-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .send-actions {
    justify-content: flex-end;
  }
}
</style>
