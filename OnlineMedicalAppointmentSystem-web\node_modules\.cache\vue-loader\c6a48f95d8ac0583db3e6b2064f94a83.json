{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\chatinfo\\ChatinfoManage.vue?vue&type=style&index=0&id=9cc65c2c&scoped=true&lang=css", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\chatinfo\\ChatinfoManage.vue", "mtime": 1749360950885}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749193686285}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749193687363}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749193686702}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5wYWdlLWhlYWRlciB7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgcGFkZGluZzogMjBweDsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLDAsMCwwLjEpOwp9CgoucGFnZS1oZWFkZXIgaDMgewogIG1hcmdpbjogMCAwIDhweCAwOwogIGNvbG9yOiAjMzMzOwogIGZvbnQtc2l6ZTogMjBweDsKfQoKLnBhZ2UtaGVhZGVyIHAgewogIG1hcmdpbjogMDsKICBjb2xvcjogIzY2NjsKICBmb250LXNpemU6IDE0cHg7Cn0KCi5tZXNzYWdlLWNvbnRlbnQgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47Cn0KCi51bnJlYWQtbWVzc2FnZSB7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY29sb3I6ICNlNzRjM2M7Cn0KCi8qIOihqOagvOagt+W8j+S8mOWMliAqLwouZWwtdGFibGUgewogIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsMCwwLDAuMSk7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi5lbC10YWJsZSB0aCB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTsKICBjb2xvcjogIzQ5NTA1NzsKICBmb250LXdlaWdodDogNjAwOwp9CgouZWwtYnV0dG9uIHsKICBib3JkZXItcmFkaXVzOiA0cHg7Cn0KCi5lbC1idXR0b24tLXByaW1hcnkgewogIGJhY2tncm91bmQtY29sb3I6ICMwMDdiZmY7CiAgYm9yZGVyLWNvbG9yOiAjMDA3YmZmOwp9CgouZWwtYnV0dG9uLS1wcmltYXJ5OmhvdmVyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDA1NmIzOwogIGJvcmRlci1jb2xvcjogIzAwNTZiMzsKfQoKLmVsLWJ1dHRvbi0tZGFuZ2VyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZGMzNTQ1OwogIGJvcmRlci1jb2xvcjogI2RjMzU0NTsKfQoKLmVsLWJ1dHRvbi0tZGFuZ2VyOmhvdmVyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjYzgyMzMzOwogIGJvcmRlci1jb2xvcjogI2M4MjMzMzsKfQo="}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\chatinfo\\ChatinfoManage.vue"], "names": [], "mappings": ";AAqMA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/admin/chatinfo/ChatinfoManage.vue", "sourceRoot": "", "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n      <div class=\"page-header\">\n        <h3>我收到的聊天</h3>\n        <p>管理患者发送的聊天消息，点击回复按钮进行在线沟通</p>\n      </div>\n\n<el-table :data=\"datalist\" border stripe style=\"width: 100%\"  v-loading=\"listLoading\"   highlight-current-row   max-height=\"600\"     size=\"small\">\n<el-table-column prop=\"lname\" label=\"患者姓名\"  align=\"center\" width=\"120\"></el-table-column>\n<el-table-column prop=\"content\" label=\"最新消息\"  align=\"center\" min-width=\"200\">\n  <template #default=\"scope\">\n    <div class=\"message-content\">\n      <span :class=\"{'unread-message': scope.row.flag == '1'}\">{{ scope.row.content }}</span>\n      <el-tag v-if=\"scope.row.flag == '1'\" type=\"danger\" size=\"small\" style=\"margin-left: 8px;\">未回复</el-tag>\n    </div>\n  </template>\n</el-table-column>\n<el-table-column prop=\"sendtime\" label=\"发送时间\"  align=\"center\" width=\"160\"></el-table-column>\n<el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n<template #default=\"scope\">\n<el-button type=\"primary\" size=\"mini\" @click=\"handleReply(scope.$index, scope.row)\" icon=\"el-icon-chat-dot-round\" style=\"padding: 3px 6px 3px 6px;\">回复</el-button>\n<el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\" style=\" padding: 3px 6px 3px 6px;\">删除</el-button>\n</template>\n</el-table-column>\n</el-table>\n<el-pagination  @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\" \n background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\"\n style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n\n    <!-- 聊天对话框 -->\n    <ChatDialog\n      v-model=\"chatDialogVisible\"\n      :patient-name=\"selectedPatient.name\"\n      :patient-id=\"selectedPatient.id\"\n      @message-sent=\"onMessageSent\"\n    />\n\n    </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nimport ChatDialog from \"../../../components/ChatDialog.vue\";\n\nexport default {\n  name: 'chatinfo',\n  components: {\n    ChatDialog\n  },\n    data() {\n      return {\n       \n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,      \n        \n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据\n        chatDialogVisible: false, //聊天对话框显示状态\n        selectedPatient: { //选中的患者信息\n          name: '',\n          id: ''\n        },\n        refreshTimer: null //自动刷新定时器\n\n      };\n    },\n    created() {\n      this.getDatas();\n      this.startAutoRefresh();\n    },\n\n    beforeUnmount() {\n      this.stopAutoRefresh();\n    },\n\n \n    methods: {    \n\n              \n       // 删除聊天\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/chatinfo/del?id=\" + row.cid;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n        },\n                \n        // 分页\n        handleCurrentChange(val) {\n          this.page.currentPage = val;\n          this.getDatas();\n        },     \n     \n        //获取列表数据\n      getDatas() {\n           var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息\n        let para = {\n            did:user.did,\n            condition:\" and a.cid in(select max(cid) from chatinfo where did=\"+user.did+\" group by lname ) \"\n          };\n          this.listLoading = true;\n          let url = base + \"/chatinfo/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;\n          request.post(url, para).then((res) => {\n            if (res.resdata.length > 0) {\n              this.isPage = true;\n            } else {\n              this.isPage = false;\n            }\n            this.page.totalCount = res.count;\n            this.datalist = res.resdata;\n            this.listLoading = false;\n          }).catch((error) => {\n            console.error('获取聊天信息列表失败:', error);\n            this.listLoading = false;\n            this.$message.error('获取聊天信息列表失败');\n          });\n        },\n        \n           \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/ChatinfoDetail\",\n             query: {\n                id: row.cid,\n              },\n          });\n        },\n    \n        // 回复聊天\n        handleReply(index, row) {\n          this.selectedPatient = {\n            name: row.lname,\n            id: row.lname // 使用用户名作为标识\n          };\n          this.chatDialogVisible = true;\n        },\n\n        // 消息发送成功回调\n        onMessageSent() {\n          // 刷新聊天列表\n          setTimeout(() => {\n            this.getDatas();\n          }, 1000);\n        },\n\n        // 开始自动刷新\n        startAutoRefresh() {\n          this.stopAutoRefresh(); // 先清除之前的定时器\n          this.refreshTimer = setInterval(() => {\n            this.getDatas();\n          }, 30000); // 每30秒刷新一次列表\n        },\n\n        // 停止自动刷新\n        stopAutoRefresh() {\n          if (this.refreshTimer) {\n            clearInterval(this.refreshTimer);\n            this.refreshTimer = null;\n          }\n        },\n\n        // 编辑\n        handleEdit(index, row) {\n          this.$router.push({\n            path: \"/ChatinfoEdit\",\n             query: {\n                id: row.cid,\n              },\n          });\n        },\n      },\n}\n\n</script>\n<style scoped>\n.page-header {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.page-header h3 {\n  margin: 0 0 8px 0;\n  color: #333;\n  font-size: 20px;\n}\n\n.page-header p {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.message-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.unread-message {\n  font-weight: bold;\n  color: #e74c3c;\n}\n\n/* 表格样式优化 */\n.el-table {\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.el-table th {\n  background-color: #f8f9fa;\n  color: #495057;\n  font-weight: 600;\n}\n\n.el-button {\n  border-radius: 4px;\n}\n\n.el-button--primary {\n  background-color: #007bff;\n  border-color: #007bff;\n}\n\n.el-button--primary:hover {\n  background-color: #0056b3;\n  border-color: #0056b3;\n}\n\n.el-button--danger {\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n\n.el-button--danger:hover {\n  background-color: #c82333;\n  border-color: #c82333;\n}\n</style>\n \n\n"]}]}