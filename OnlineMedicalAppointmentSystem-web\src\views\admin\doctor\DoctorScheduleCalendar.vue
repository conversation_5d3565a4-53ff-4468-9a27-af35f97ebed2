<template>
  <div class="schedule-calendar">
    <div class="header">
 
      <div class="doctor-info" v-if="doctorInfo">
        <span>医生：{{ doctorInfo.dname }}</span>
        <span>科室：{{ doctorInfo.pname }}</span>
      </div>
    </div>

    <div class="calendar-container" v-loading="loading">
      <!-- 日期表头 -->
      <div class="schedule-header">
        <div class="time-column">时间段</div>
        <div class="date-column" v-for="(date, index) in dates" :key="index">
          <div class="date">{{ date }}</div>
          <div class="week-day">{{ weeks[index] }}</div>
        </div>
      </div>

      <!-- 排班表格 -->
      <div class="schedule-body">
        <div class="schedule-row" v-for="(timeSlot, timeIndex) in timeSlots" :key="timeIndex">
          <!-- 时间段 -->
          <div class="time-cell">{{ timeSlot }}</div>

          <!-- 每天的排班情况 -->
          <div 
            class="schedule-cell" 
            v-for="(weekDay, dayIndex) in weeks" 
            :key="dayIndex"
            :class="{ 'has-schedule': getScheduleInfo(timeSlot, weekDay).hasSchedule }"
          >
            <div v-if="getScheduleInfo(timeSlot, weekDay).hasSchedule" class="schedule-info">
              <div class="status available">可预约</div>
              <div class="remaining">
                剩余：{{ getScheduleInfo(timeSlot, weekDay).availableSlots }}
              </div>
              <div class="total">
                总数：{{ getScheduleInfo(timeSlot, weekDay).totalSlots }}
              </div>
            </div>
            <div v-else class="no-schedule">
              <div class="status unavailable">未排班</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="statistics">
      <el-card class="stat-card">
        <div class="stat-item">
          <span class="stat-label">本周排班天数：</span>
          <span class="stat-value">{{ weeklyStats.scheduledDays }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">本周总号数：</span>
          <span class="stat-value">{{ weeklyStats.totalSlots }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">本周已预约：</span>
          <span class="stat-value">{{ weeklyStats.bookedSlots }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">本周剩余：</span>
          <span class="stat-value">{{ weeklyStats.availableSlots }}</span>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import request, { base } from "../../../../utils/http";

export default {
  name: 'DoctorScheduleCalendar',
  data() {
    return {
      loading: false,
      doctorInfo: null,
      dates: [],
      weeks: [],
      timeSlots: [
        "8:00-9:00",
        "9:00-10:00", 
        "10:00-11:00",
        "11:00-12:00",
        "14:00-15:00",
        "15:00-16:00",
        "16:00-17:00"
      ],
      scheduleData: [],
      weeklyStats: {
        scheduledDays: 0,
        totalSlots: 0,
        bookedSlots: 0,
        availableSlots: 0
      }
    };
  },
  created() {
    this.initDates();
    this.getDoctorInfo();
    this.getScheduleData();
  },
  methods: {
    // 初始化未来7天的日期
    initDates() {
      const today = new Date();
      const weekMap = {
        0: "星期日",
        1: "星期一", 
        2: "星期二",
        3: "星期三",
        4: "星期四",
        5: "星期五",
        6: "星期六"
      };

      for (let i = 0; i < 7; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const dateStr = `${month}-${day}`;
        
        this.dates.push(dateStr);
        this.weeks.push(weekMap[date.getDay()]);
      }
    },

    // 获取医生信息
    getDoctorInfo() {
      const user = JSON.parse(sessionStorage.getItem("user"));
      if (!user || !user.did) {
        this.$message.error('请先登录');
        return;
      }

      this.loading = true;
      const url = base + "/doctor/get?id=" + user.did;
      request.post(url).then((res) => {
        this.doctorInfo = res.resdata;
        this.loading = false;
      }).catch((error) => {
        console.error('获取医生信息失败:', error);
        this.loading = false;
        this.$message.error('获取医生信息失败');
      });
    },

    // 获取排班数据
    getScheduleData() {
      const user = JSON.parse(sessionStorage.getItem("user"));
      if (!user || !user.did) {
        return;
      }

      this.loading = true;
      const param = {
        f: 5,
        did: user.did,
        loadmsg: '正在加载中'
      };

      const url = base + "/plans/list3?currentPage=1&pageSize=500";
      request.post(url, param).then((res) => {
        if (res.resdata && res.resdata.length > 0) {
          this.scheduleData = res.resdata;
          this.calculateWeeklyStats();
        }
        this.loading = false;
      }).catch((error) => {
        console.error('获取排班数据失败:', error);
        this.loading = false;
        this.$message.error('获取排班数据失败');
      });
    },

    // 获取指定时间段和星期的排班信息
    getScheduleInfo(timeSlot, weekDay) {
      const schedule = this.scheduleData.find(
        item => item.ptime === timeSlot && item.weeks === weekDay
      );

      if (schedule) {
        const totalSlots = parseInt(schedule.people) || 0;
        const bookedSlots = parseInt(schedule.by1) || 0;
        const availableSlots = totalSlots - bookedSlots;

        return {
          hasSchedule: true,
          totalSlots: totalSlots,
          bookedSlots: bookedSlots,
          availableSlots: availableSlots > 0 ? availableSlots : 0
        };
      }

      return {
        hasSchedule: false,
        totalSlots: 0,
        bookedSlots: 0,
        availableSlots: 0
      };
    },

    // 计算本周统计数据
    calculateWeeklyStats() {
      let scheduledDays = new Set();
      let totalSlots = 0;
      let bookedSlots = 0;

      this.scheduleData.forEach(schedule => {
        if (this.weeks.includes(schedule.weeks)) {
          scheduledDays.add(schedule.weeks);
          totalSlots += parseInt(schedule.people) || 0;
          bookedSlots += parseInt(schedule.by1) || 0;
        }
      });

      this.weeklyStats = {
        scheduledDays: scheduledDays.size,
        totalSlots: totalSlots,
        bookedSlots: bookedSlots,
        availableSlots: totalSlots - bookedSlots
      };
    }
  }
};
</script>

<style scoped>
.schedule-calendar {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 24px;
}

.doctor-info {
  color: #666;
  font-size: 14px;
}

.doctor-info span {
  margin-right: 20px;
}

.calendar-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.schedule-header {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
}

.time-column {
  width: 120px;
  padding: 15px 10px;
  text-align: center;
  font-weight: bold;
  background-color: #e9ecef;
  border-right: 1px solid #dee2e6;
}

.date-column {
  flex: 1;
  padding: 10px;
  text-align: center;
  border-right: 1px solid #dee2e6;
  min-width: 100px;
}

.date {
  font-weight: bold;
  font-size: 16px;
  color: #333;
}

.week-day {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.schedule-body {
  border-bottom: 1px solid #dee2e6;
}

.schedule-row {
  display: flex;
  border-bottom: 1px solid #dee2e6;
}

.schedule-row:last-child {
  border-bottom: none;
}

.time-cell {
  width: 120px;
  padding: 20px 10px;
  text-align: center;
  background-color: #f8f9fa;
  border-right: 1px solid #dee2e6;
  font-weight: 500;
  color: #495057;
}

.schedule-cell {
  flex: 1;
  padding: 15px 10px;
  text-align: center;
  border-right: 1px solid #dee2e6;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 100px;
}

.schedule-cell.has-schedule {
  background-color: #e8f5e8;
}

.schedule-info {
  width: 100%;
}

.status.available {
  color: #28a745;
  font-weight: bold;
  margin-bottom: 5px;
}

.status.unavailable {
  color: #6c757d;
  font-style: italic;
}

.remaining, .total {
  font-size: 12px;
  color: #666;
  margin: 2px 0;
}

.no-schedule {
  width: 100%;
  color: #999;
}

.statistics {
  margin-top: 20px;
}

.stat-card {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-item {
  display: inline-block;
  margin-right: 30px;
  margin-bottom: 10px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.stat-value {
  color: #333;
  font-weight: bold;
  font-size: 16px;
  margin-left: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .schedule-header,
  .schedule-row {
    min-width: 800px;
  }
  
  .calendar-container {
    overflow-x: auto;
  }
  
  .stat-item {
    display: block;
    margin-bottom: 15px;
  }
}
</style>
