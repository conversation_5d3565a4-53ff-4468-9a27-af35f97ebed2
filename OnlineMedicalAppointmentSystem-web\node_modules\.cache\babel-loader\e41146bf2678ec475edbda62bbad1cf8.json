{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue", "mtime": 1749361137943}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "props", "modelValue", "type", "Boolean", "default", "patientName", "String", "patientId", "emits", "data", "messages", "inputMessage", "sending", "refreshTimer", "computed", "visible", "get", "set", "value", "$emit", "watch", "newVal", "loadMessages", "startAutoRefresh", "stopAutoRefresh", "methods", "user", "JSON", "parse", "sessionStorage", "getItem", "param", "lname", "did", "url", "post", "then", "res", "resdata", "sort", "a", "b", "Date", "sendtime", "$nextTick", "scrollToBottom", "catch", "error", "console", "$message", "sendMessage", "trim", "warning", "content", "flag", "toLocaleString", "success", "setTimeout", "container", "$refs", "chatContainer", "scrollTop", "scrollHeight", "setInterval", "clearInterval", "handleClose", "beforeUnmount"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    v-model=\"visible\"\n    :title=\"`与${patientName}的对话`\"\n    width=\"700px\"\n    :before-close=\"handleClose\"\n    class=\"chat-dialog\"\n    top=\"5vh\"\n  >\n    <!-- 聊天头部信息 -->\n    <div class=\"chat-header\">\n      <div class=\"patient-info\">\n        <div class=\"avatar\">\n          <i class=\"el-icon-user-solid\"></i>\n        </div>\n        <div class=\"info\">\n          <div class=\"name\">{{ patientName }}</div>\n          <div class=\"status\">在线咨询</div>\n        </div>\n      </div>\n      <div class=\"chat-actions\">\n        <el-button size=\"small\" type=\"text\" @click=\"loadMessages\">\n          <i class=\"el-icon-refresh\"></i> 刷新\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 聊天消息区域 -->\n    <div class=\"chat-container\" ref=\"chatContainer\">\n      <div class=\"chat-messages\">\n        <div v-if=\"messages.length === 0\" class=\"empty-messages\">\n          <i class=\"el-icon-chat-dot-round\"></i>\n          <p>暂无聊天记录</p>\n        </div>\n        <div\n          v-for=\"(message, index) in messages\"\n          :key=\"index\"\n          :class=\"['message-item', message.flag == '2' ? 'doctor-message' : 'patient-message']\"\n        >\n          <div class=\"avatar-wrapper\">\n            <div class=\"message-avatar\" :class=\"message.flag == '2' ? 'doctor-avatar' : 'patient-avatar'\">\n              <i :class=\"message.flag == '2' ? 'el-icon-s-custom' : 'el-icon-user-solid'\"></i>\n            </div>\n          </div>\n          <div class=\"message-wrapper\">\n            <div class=\"message-content\">\n              <div class=\"message-text\">{{ message.content }}</div>\n            </div>\n            <div class=\"message-time\">{{ formatTime(message.sendtime) }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 输入区域 -->\n    <div class=\"chat-input\">\n      <div class=\"input-wrapper\">\n        <el-input\n          v-model=\"inputMessage\"\n          type=\"textarea\"\n          :rows=\"3\"\n          placeholder=\"请输入回复内容...\"\n          maxlength=\"500\"\n          show-word-limit\n          @keyup.ctrl.enter=\"sendMessage\"\n          class=\"message-input\"\n        ></el-input>\n        <div class=\"input-actions\">\n          <div class=\"input-tips\">\n            <span class=\"tip-item\">\n              <i class=\"el-icon-info\"></i>\n              Ctrl + Enter 快速发送\n            </span>\n            <span class=\"tip-item\">\n              <i class=\"el-icon-time\"></i>\n              每5秒自动刷新\n            </span>\n          </div>\n          <div class=\"send-actions\">\n            <el-button @click=\"inputMessage = ''\" size=\"small\">清空</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"sendMessage\"\n              :loading=\"sending\"\n              :disabled=\"!inputMessage.trim()\"\n              class=\"send-btn\"\n            >\n              <i class=\"el-icon-s-promotion\"></i>\n              发送\n            </el-button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport request, { base } from \"../../utils/http\";\n\nexport default {\n  name: 'ChatDialog',\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    patientName: {\n      type: String,\n      default: ''\n    },\n    patientId: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['update:modelValue', 'message-sent'],\n  data() {\n    return {\n      messages: [],\n      inputMessage: '',\n      sending: false,\n      refreshTimer: null\n    };\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.modelValue;\n      },\n      set(value) {\n        this.$emit('update:modelValue', value);\n      }\n    }\n  },\n  watch: {\n    visible(newVal) {\n      if (newVal) {\n        this.loadMessages();\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    }\n  },\n  methods: {\n    // 加载聊天消息\n    loadMessages() {\n      if (!this.patientId) return;\n\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      const param = {\n        lname: this.patientName,\n        did: user.did\n      };\n\n      const url = base + \"/chatinfo/list2?currentPage=1&pageSize=5000\";\n      request.post(url, param).then((res) => {\n        if (res.resdata) {\n          this.messages = res.resdata.sort((a, b) => {\n            return new Date(a.sendtime) - new Date(b.sendtime);\n          });\n          this.$nextTick(() => {\n            this.scrollToBottom();\n          });\n        }\n      }).catch((error) => {\n        console.error('加载聊天消息失败:', error);\n        this.$message.error('加载聊天消息失败');\n      });\n    },\n\n    // 发送消息\n    sendMessage() {\n      if (!this.inputMessage.trim()) {\n        this.$message.warning('请输入消息内容');\n        return;\n      }\n\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      this.sending = true;\n\n      const param = {\n        lname: this.patientName,\n        did: user.did,\n        content: this.inputMessage,\n        flag: \"2\", // 医生发送的消息\n        sendtime: new Date().toLocaleString()\n      };\n\n      const url = base + \"/chatinfo/add\";\n      request.post(url, param).then((res) => {\n        this.sending = false;\n        this.inputMessage = '';\n        this.$message.success('发送成功');\n\n        // 立即刷新消息列表\n        setTimeout(() => {\n          this.loadMessages();\n        }, 500);\n\n        // 通知父组件消息已发送\n        this.$emit('message-sent');\n      }).catch((error) => {\n        this.sending = false;\n        console.error('发送消息失败:', error);\n        this.$message.error('发送消息失败');\n      });\n    },\n\n    // 滚动到底部\n    scrollToBottom() {\n      const container = this.$refs.chatContainer;\n      if (container) {\n        container.scrollTop = container.scrollHeight;\n      }\n    },\n\n    // 开始自动刷新\n    startAutoRefresh() {\n      this.stopAutoRefresh(); // 先清除之前的定时器\n      this.refreshTimer = setInterval(() => {\n        this.loadMessages();\n      }, 5000); // 每5秒刷新一次\n    },\n\n    // 停止自动刷新\n    stopAutoRefresh() {\n      if (this.refreshTimer) {\n        clearInterval(this.refreshTimer);\n        this.refreshTimer = null;\n      }\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.stopAutoRefresh();\n      this.visible = false;\n      this.inputMessage = '';\n      this.messages = [];\n    }\n  },\n  beforeUnmount() {\n    this.stopAutoRefresh();\n  }\n};\n</script>\n\n<style scoped>\n.chat-dialog {\n  .el-dialog__body {\n    padding: 0;\n  }\n}\n\n.chat-container {\n  height: 400px;\n  overflow-y: auto;\n  padding: 20px;\n  background-color: #f5f5f5;\n}\n\n.chat-messages {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.message-item {\n  display: flex;\n  max-width: 80%;\n}\n\n.patient-message {\n  justify-content: flex-start;\n}\n\n.doctor-message {\n  justify-content: flex-end;\n  align-self: flex-end;\n}\n\n.message-content {\n  background: white;\n  border-radius: 12px;\n  padding: 12px 16px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  position: relative;\n}\n\n.doctor-message .message-content {\n  background: #07c160;\n  color: white;\n}\n\n.patient-message .message-content::before {\n  content: '';\n  position: absolute;\n  left: -8px;\n  top: 12px;\n  width: 0;\n  height: 0;\n  border-top: 8px solid transparent;\n  border-bottom: 8px solid transparent;\n  border-right: 8px solid white;\n}\n\n.doctor-message .message-content::before {\n  content: '';\n  position: absolute;\n  right: -8px;\n  top: 12px;\n  width: 0;\n  height: 0;\n  border-top: 8px solid transparent;\n  border-bottom: 8px solid transparent;\n  border-left: 8px solid #07c160;\n}\n\n.message-text {\n  font-size: 14px;\n  line-height: 1.4;\n  word-wrap: break-word;\n}\n\n.message-time {\n  font-size: 12px;\n  margin-top: 8px;\n  opacity: 0.7;\n}\n\n.doctor-message .message-time {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.patient-message .message-time {\n  color: #999;\n}\n\n.chat-input {\n  padding: 20px;\n  border-top: 1px solid #eee;\n  background: white;\n}\n\n.input-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 10px;\n}\n\n.input-tip {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 滚动条样式 */\n.chat-container::-webkit-scrollbar {\n  width: 6px;\n}\n\n.chat-container::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.chat-container::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.chat-container::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n</style>\n"], "mappings": "AAkGA,OAAOA,OAAO,IAAIC,IAAG,QAAS,kBAAkB;AAEhD,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,KAAK,EAAE;IACLC,UAAU,EAAE;MACVC,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDC,WAAW,EAAE;MACXH,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX,CAAC;IACDG,SAAS,EAAE;MACTL,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX;EACF,CAAC;EACDI,KAAK,EAAE,CAAC,mBAAmB,EAAE,cAAc,CAAC;EAC5CC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,KAAK;MACdC,YAAY,EAAE;IAChB,CAAC;EACH,CAAC;EACDC,QAAQ,EAAE;IACRC,OAAO,EAAE;MACPC,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAACf,UAAU;MACxB,CAAC;MACDgB,GAAGA,CAACC,KAAK,EAAE;QACT,IAAI,CAACC,KAAK,CAAC,mBAAmB,EAAED,KAAK,CAAC;MACxC;IACF;EACF,CAAC;EACDE,KAAK,EAAE;IACLL,OAAOA,CAACM,MAAM,EAAE;MACd,IAAIA,MAAM,EAAE;QACV,IAAI,CAACC,YAAY,CAAC,CAAC;QACnB,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACzB,OAAO;QACL,IAAI,CAACC,eAAe,CAAC,CAAC;MACxB;IACF;EACF,CAAC;EACDC,OAAO,EAAE;IACP;IACAH,YAAYA,CAAA,EAAG;MACb,IAAI,CAAC,IAAI,CAACf,SAAS,EAAE;MAErB,MAAMmB,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;MACvD,MAAMC,KAAI,GAAI;QACZC,KAAK,EAAE,IAAI,CAAC3B,WAAW;QACvB4B,GAAG,EAAEP,IAAI,CAACO;MACZ,CAAC;MAED,MAAMC,GAAE,GAAIpC,IAAG,GAAI,6CAA6C;MAChED,OAAO,CAACsC,IAAI,CAACD,GAAG,EAAEH,KAAK,CAAC,CAACK,IAAI,CAAEC,GAAG,IAAK;QACrC,IAAIA,GAAG,CAACC,OAAO,EAAE;UACf,IAAI,CAAC5B,QAAO,GAAI2B,GAAG,CAACC,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YACzC,OAAO,IAAIC,IAAI,CAACF,CAAC,CAACG,QAAQ,IAAI,IAAID,IAAI,CAACD,CAAC,CAACE,QAAQ,CAAC;UACpD,CAAC,CAAC;UACF,IAAI,CAACC,SAAS,CAAC,MAAM;YACnB,IAAI,CAACC,cAAc,CAAC,CAAC;UACvB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,CAACC,KAAK,CAAEC,KAAK,IAAK;QAClBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAACE,QAAQ,CAACF,KAAK,CAAC,UAAU,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC;IAED;IACAG,WAAWA,CAAA,EAAG;MACZ,IAAI,CAAC,IAAI,CAACvC,YAAY,CAACwC,IAAI,CAAC,CAAC,EAAE;QAC7B,IAAI,CAACF,QAAQ,CAACG,OAAO,CAAC,SAAS,CAAC;QAChC;MACF;MAEA,MAAM1B,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;MACvD,IAAI,CAAClB,OAAM,GAAI,IAAI;MAEnB,MAAMmB,KAAI,GAAI;QACZC,KAAK,EAAE,IAAI,CAAC3B,WAAW;QACvB4B,GAAG,EAAEP,IAAI,CAACO,GAAG;QACboB,OAAO,EAAE,IAAI,CAAC1C,YAAY;QAC1B2C,IAAI,EAAE,GAAG;QAAE;QACXX,QAAQ,EAAE,IAAID,IAAI,CAAC,CAAC,CAACa,cAAc,CAAC;MACtC,CAAC;MAED,MAAMrB,GAAE,GAAIpC,IAAG,GAAI,eAAe;MAClCD,OAAO,CAACsC,IAAI,CAACD,GAAG,EAAEH,KAAK,CAAC,CAACK,IAAI,CAAEC,GAAG,IAAK;QACrC,IAAI,CAACzB,OAAM,GAAI,KAAK;QACpB,IAAI,CAACD,YAAW,GAAI,EAAE;QACtB,IAAI,CAACsC,QAAQ,CAACO,OAAO,CAAC,MAAM,CAAC;;QAE7B;QACAC,UAAU,CAAC,MAAM;UACf,IAAI,CAACnC,YAAY,CAAC,CAAC;QACrB,CAAC,EAAE,GAAG,CAAC;;QAEP;QACA,IAAI,CAACH,KAAK,CAAC,cAAc,CAAC;MAC5B,CAAC,CAAC,CAAC2B,KAAK,CAAEC,KAAK,IAAK;QAClB,IAAI,CAACnC,OAAM,GAAI,KAAK;QACpBoC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B,IAAI,CAACE,QAAQ,CAACF,KAAK,CAAC,QAAQ,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC;IAED;IACAF,cAAcA,CAAA,EAAG;MACf,MAAMa,SAAQ,GAAI,IAAI,CAACC,KAAK,CAACC,aAAa;MAC1C,IAAIF,SAAS,EAAE;QACbA,SAAS,CAACG,SAAQ,GAAIH,SAAS,CAACI,YAAY;MAC9C;IACF,CAAC;IAED;IACAvC,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACC,eAAe,CAAC,CAAC,EAAE;MACxB,IAAI,CAACX,YAAW,GAAIkD,WAAW,CAAC,MAAM;QACpC,IAAI,CAACzC,YAAY,CAAC,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC,EAAE;IACZ,CAAC;IAED;IACAE,eAAeA,CAAA,EAAG;MAChB,IAAI,IAAI,CAACX,YAAY,EAAE;QACrBmD,aAAa,CAAC,IAAI,CAACnD,YAAY,CAAC;QAChC,IAAI,CAACA,YAAW,GAAI,IAAI;MAC1B;IACF,CAAC;IAED;IACAoD,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACzC,eAAe,CAAC,CAAC;MACtB,IAAI,CAACT,OAAM,GAAI,KAAK;MACpB,IAAI,CAACJ,YAAW,GAAI,EAAE;MACtB,IAAI,CAACD,QAAO,GAAI,EAAE;IACpB;EACF,CAAC;EACDwD,aAAaA,CAAA,EAAG;IACd,IAAI,CAAC1C,eAAe,CAAC,CAAC;EACxB;AACF,CAAC", "ignoreList": []}]}