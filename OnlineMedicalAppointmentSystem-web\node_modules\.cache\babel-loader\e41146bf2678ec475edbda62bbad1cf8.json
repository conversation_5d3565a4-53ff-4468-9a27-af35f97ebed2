{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue", "mtime": 1749362335849}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "props", "modelValue", "type", "Boolean", "default", "patientName", "String", "patientId", "emits", "data", "messages", "inputMessage", "sending", "refreshTimer", "fileBasePath", "computed", "visible", "get", "set", "value", "$emit", "watch", "newVal", "loadMessages", "startAutoRefresh", "stopAutoRefresh", "methods", "user", "JSON", "parse", "sessionStorage", "getItem", "param", "lname", "did", "url", "post", "then", "res", "resdata", "sort", "a", "b", "Date", "sendtime", "$nextTick", "scrollToBottom", "catch", "error", "console", "$message", "sendMessage", "trim", "warning", "content", "flag", "toLocaleString", "success", "setTimeout", "container", "$refs", "chatContainer", "scrollTop", "scrollHeight", "setInterval", "clearInterval", "formatTime", "timeStr", "date", "now", "diff", "getDate", "toLocaleTimeString", "hour", "minute", "yesterday", "setDate", "toLocaleDateString", "e", "getAvatarUrl", "message", "by2", "by1", "handleImageError", "event", "img", "target", "messageElement", "closest", "isDoctor", "classList", "contains", "src", "getDefaultAvatar", "getPatientAvatarUrl", "patientMessage", "find", "msg", "handleHeaderImageError", "handleClose", "beforeUnmount"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\ChatDialog.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    v-model=\"visible\"\n    :title=\"`与${patientName}的对话`\"\n    width=\"700px\"\n    :before-close=\"handleClose\"\n    class=\"chat-dialog\"\n    top=\"5vh\"\n  >\n    <!-- 聊天头部信息 -->\n    <div class=\"chat-header\">\n      <div class=\"patient-info\">\n        <div class=\"avatar\">\n          <img\n            :src=\"getPatientAvatarUrl()\"\n            :alt=\"patientName\"\n            @error=\"handleHeaderImageError\"\n          />\n        </div>\n        <div class=\"info\">\n          <div class=\"name\">{{ patientName }}</div>\n          <div class=\"status\">在线咨询</div>\n        </div>\n      </div>\n      <div class=\"chat-actions\">\n        <el-button size=\"small\" type=\"text\" @click=\"loadMessages\">\n          <i class=\"el-icon-refresh\"></i> 刷新\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 聊天消息区域 -->\n    <div class=\"chat-container\" ref=\"chatContainer\">\n      <div class=\"chat-messages\">\n        <div v-if=\"messages.length === 0\" class=\"empty-messages\">\n          <i class=\"el-icon-chat-dot-round\"></i>\n          <p>暂无聊天记录</p>\n        </div>\n        <div\n          v-for=\"(message, index) in messages\"\n          :key=\"index\"\n          :class=\"['message-item', message.flag == '2' ? 'doctor-message' : 'patient-message']\"\n        >\n          <div class=\"avatar-wrapper\">\n            <div class=\"message-avatar\" :class=\"message.flag == '2' ? 'doctor-avatar' : 'patient-avatar'\">\n              <img\n                :src=\"getAvatarUrl(message)\"\n                :alt=\"message.flag == '2' ? '医生' : '患者'\"\n                @error=\"handleImageError\"\n              />\n            </div>\n          </div>\n          <div class=\"message-wrapper\">\n            <div class=\"message-content\">\n              <div class=\"message-text\">{{ message.content }}</div>\n            </div>\n            <div class=\"message-time\">{{ formatTime(message.sendtime) }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 输入区域 -->\n    <div class=\"chat-input\">\n      <div class=\"input-wrapper\">\n        <el-input\n          v-model=\"inputMessage\"\n          type=\"textarea\"\n          :rows=\"3\"\n          placeholder=\"请输入回复内容...\"\n          maxlength=\"500\"\n          show-word-limit\n          @keyup.ctrl.enter=\"sendMessage\"\n          class=\"message-input\"\n        ></el-input>\n        <div class=\"input-actions\">\n          <div class=\"input-tips\">\n            <span class=\"tip-item\">\n              <i class=\"el-icon-info\"></i>\n              Ctrl + Enter 快速发送\n            </span>\n            <span class=\"tip-item\">\n              <i class=\"el-icon-time\"></i>\n              每5秒自动刷新\n            </span>\n          </div>\n          <div class=\"send-actions\">\n            <el-button @click=\"inputMessage = ''\" size=\"small\">清空</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"sendMessage\"\n              :loading=\"sending\"\n              :disabled=\"!inputMessage.trim()\"\n              class=\"send-btn\"\n            >\n              <i class=\"el-icon-s-promotion\"></i>\n              发送\n            </el-button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport request, { base } from \"../../utils/http\";\n\nexport default {\n  name: 'ChatDialog',\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    patientName: {\n      type: String,\n      default: ''\n    },\n    patientId: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['update:modelValue', 'message-sent'],\n  data() {\n    return {\n      messages: [],\n      inputMessage: '',\n      sending: false,\n      refreshTimer: null,\n      fileBasePath: 'http://127.0.0.1:8088/OnlineMedicalAppointmentSystem_Server/upload/'\n    };\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.modelValue;\n      },\n      set(value) {\n        this.$emit('update:modelValue', value);\n      }\n    }\n  },\n  watch: {\n    visible(newVal) {\n      if (newVal) {\n        this.loadMessages();\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    }\n  },\n  methods: {\n    // 加载聊天消息\n    loadMessages() {\n      if (!this.patientId) return;\n\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      const param = {\n        lname: this.patientName,\n        did: user.did\n      };\n\n      const url = base + \"/chatinfo/list2?currentPage=1&pageSize=5000\";\n      request.post(url, param).then((res) => {\n        if (res.resdata) {\n          this.messages = res.resdata.sort((a, b) => {\n            return new Date(a.sendtime) - new Date(b.sendtime);\n          });\n          this.$nextTick(() => {\n            this.scrollToBottom();\n          });\n        }\n      }).catch((error) => {\n        console.error('加载聊天消息失败:', error);\n        this.$message.error('加载聊天消息失败');\n      });\n    },\n\n    // 发送消息\n    sendMessage() {\n      if (!this.inputMessage.trim()) {\n        this.$message.warning('请输入消息内容');\n        return;\n      }\n\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      this.sending = true;\n\n      const param = {\n        lname: this.patientName,\n        did: user.did,\n        content: this.inputMessage,\n        flag: \"2\", // 医生发送的消息\n        sendtime: new Date().toLocaleString()\n      };\n\n      const url = base + \"/chatinfo/add\";\n      request.post(url, param).then((res) => {\n        this.sending = false;\n        this.inputMessage = '';\n        this.$message.success('发送成功');\n\n        // 立即刷新消息列表\n        setTimeout(() => {\n          this.loadMessages();\n        }, 500);\n\n        // 通知父组件消息已发送\n        this.$emit('message-sent');\n      }).catch((error) => {\n        this.sending = false;\n        console.error('发送消息失败:', error);\n        this.$message.error('发送消息失败');\n      });\n    },\n\n    // 滚动到底部\n    scrollToBottom() {\n      const container = this.$refs.chatContainer;\n      if (container) {\n        container.scrollTop = container.scrollHeight;\n      }\n    },\n\n    // 开始自动刷新\n    startAutoRefresh() {\n      this.stopAutoRefresh(); // 先清除之前的定时器\n      this.refreshTimer = setInterval(() => {\n        this.loadMessages();\n      }, 5000); // 每5秒刷新一次\n    },\n\n    // 停止自动刷新\n    stopAutoRefresh() {\n      if (this.refreshTimer) {\n        clearInterval(this.refreshTimer);\n        this.refreshTimer = null;\n      }\n    },\n\n    // 格式化时间\n    formatTime(timeStr) {\n      if (!timeStr) return '';\n      try {\n        const date = new Date(timeStr);\n        const now = new Date();\n        const diff = now - date;\n\n        // 如果是今天\n        if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {\n          return date.toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit'\n          });\n        }\n\n        // 如果是昨天\n        const yesterday = new Date(now);\n        yesterday.setDate(now.getDate() - 1);\n        if (date.getDate() === yesterday.getDate()) {\n          return '昨天 ' + date.toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit'\n          });\n        }\n\n        // 其他日期\n        return date.toLocaleDateString('zh-CN') + ' ' +\n               date.toLocaleTimeString('zh-CN', {\n                 hour: '2-digit',\n                 minute: '2-digit'\n               });\n      } catch (e) {\n        return timeStr;\n      }\n    },\n\n    // 获取头像URL\n    getAvatarUrl(message) {\n      if (message.flag == '2') {\n        // 医生头像 - 使用by2字段\n        return message.by2 = this.fileBasePath + message.by2;\n      } else {\n        // 患者头像 - 使用by1字段\n        return message.by1 = this.fileBasePath + message.by1 ;\n      }\n    },\n\n\n\n    // 处理图片加载错误\n    handleImageError(event) {\n      const img = event.target;\n      const messageElement = img.closest('.message-item');\n      const isDoctor = messageElement.classList.contains('doctor-message');\n      img.src = this.getDefaultAvatar(isDoctor ? 'doctor' : 'patient');\n    },\n\n    // 获取患者头像URL（用于聊天头部）\n    getPatientAvatarUrl() {\n      // 从最新的消息中获取患者头像\n      const patientMessage = this.messages.find(msg => msg.flag == '1');\n      if (patientMessage && patientMessage.by1) {\n        return this.fileBasePath + patientMessage.by1;\n      }\n    },\n\n    // 处理头部图片加载错误\n    handleHeaderImageError(event) {\n      event.target.src = this.getDefaultAvatar('patient');\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.stopAutoRefresh();\n      this.visible = false;\n      this.inputMessage = '';\n      this.messages = [];\n    }\n  },\n  beforeUnmount() {\n    this.stopAutoRefresh();\n  }\n};\n</script>\n\n<style scoped>\n/* 对话框整体样式 */\n.chat-dialog :deep(.el-dialog) {\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.chat-dialog :deep(.el-dialog__header) {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 20px 24px;\n  margin: 0;\n}\n\n.chat-dialog :deep(.el-dialog__title) {\n  color: white;\n  font-weight: 600;\n  font-size: 18px;\n}\n\n.chat-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {\n  color: white;\n  font-size: 20px;\n}\n\n.chat-dialog :deep(.el-dialog__body) {\n  padding: 0;\n  background: #f8f9fa;\n}\n\n/* 聊天头部 */\n.chat-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 24px;\n  background: white;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.patient-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.patient-info .avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 18px;\n  overflow: hidden;\n  border: 2px solid #fff;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.patient-info .avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  border-radius: 50%;\n}\n\n.patient-info .info .name {\n  font-weight: 600;\n  color: #333;\n  font-size: 16px;\n}\n\n.patient-info .info .status {\n  font-size: 12px;\n  color: #28a745;\n  margin-top: 2px;\n}\n\n.chat-actions .el-button {\n  color: #6c757d;\n}\n\n/* 聊天容器 */\n.chat-container {\n  height: 450px;\n  overflow-y: auto;\n  padding: 20px 24px;\n  background: #f8f9fa;\n}\n\n.empty-messages {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  color: #6c757d;\n}\n\n.empty-messages i {\n  font-size: 48px;\n  margin-bottom: 12px;\n  opacity: 0.5;\n}\n\n.empty-messages p {\n  margin: 0;\n  font-size: 14px;\n}\n\n/* 消息列表 */\n.chat-messages {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.message-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  max-width: 85%;\n}\n\n.patient-message {\n  align-self: flex-start;\n}\n\n.doctor-message {\n  align-self: flex-end;\n  flex-direction: row-reverse;\n}\n\n/* 头像 */\n.message-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  overflow: hidden;\n  border: 2px solid #fff;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.message-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  border-radius: 50%;\n}\n\n.patient-avatar {\n  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);\n}\n\n.doctor-avatar {\n  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);\n}\n\n/* 消息内容 */\n.message-wrapper {\n  flex: 1;\n  min-width: 0;\n}\n\n.message-content {\n  border-radius: 18px;\n  padding: 12px 16px;\n  position: relative;\n  word-wrap: break-word;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.patient-message .message-content {\n  background: white;\n  color: #333;\n  border-bottom-left-radius: 6px;\n}\n\n.doctor-message .message-content {\n  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);\n  color: white;\n  border-bottom-right-radius: 6px;\n}\n\n.message-text {\n  font-size: 14px;\n  line-height: 1.5;\n  word-break: break-word;\n}\n\n.message-time {\n  font-size: 11px;\n  margin-top: 4px;\n  opacity: 0.7;\n  text-align: center;\n}\n\n.doctor-message .message-time {\n  text-align: right;\n}\n\n.patient-message .message-time {\n  text-align: left;\n}\n\n/* 输入区域 */\n.chat-input {\n  background: white;\n  border-top: 1px solid #e9ecef;\n  padding: 0;\n}\n\n.input-wrapper {\n  padding: 20px 24px;\n}\n\n.message-input :deep(.el-textarea__inner) {\n  border: 2px solid #e9ecef;\n  border-radius: 12px;\n  padding: 12px 16px;\n  font-size: 14px;\n  line-height: 1.5;\n  resize: none;\n  transition: all 0.3s ease;\n}\n\n.message-input :deep(.el-textarea__inner):focus {\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.input-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 16px;\n}\n\n.input-tips {\n  display: flex;\n  gap: 16px;\n}\n\n.tip-item {\n  font-size: 12px;\n  color: #6c757d;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.tip-item i {\n  font-size: 14px;\n}\n\n.send-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.send-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  border-radius: 20px;\n  padding: 8px 20px;\n  font-weight: 600;\n}\n\n.send-btn:hover {\n  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n}\n\n.send-btn:disabled {\n  background: #e9ecef;\n  color: #6c757d;\n  transform: none;\n  box-shadow: none;\n}\n\n/* 滚动条样式 */\n.chat-container::-webkit-scrollbar {\n  width: 6px;\n}\n\n.chat-container::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n.chat-container::-webkit-scrollbar-thumb {\n  background: rgba(0,0,0,0.2);\n  border-radius: 3px;\n}\n\n.chat-container::-webkit-scrollbar-thumb:hover {\n  background: rgba(0,0,0,0.3);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .chat-dialog :deep(.el-dialog) {\n    width: 95% !important;\n    margin: 0 auto;\n  }\n\n  .chat-container {\n    height: 350px;\n    padding: 16px;\n  }\n\n  .input-wrapper {\n    padding: 16px;\n  }\n\n  .input-tips {\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .input-actions {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 12px;\n  }\n\n  .send-actions {\n    justify-content: flex-end;\n  }\n}\n</style>\n"], "mappings": ";;AA0GA,OAAOA,OAAO,IAAIC,IAAG,QAAS,kBAAkB;AAEhD,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,KAAK,EAAE;IACLC,UAAU,EAAE;MACVC,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDC,WAAW,EAAE;MACXH,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX,CAAC;IACDG,SAAS,EAAE;MACTL,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX;EACF,CAAC;EACDI,KAAK,EAAE,CAAC,mBAAmB,EAAE,cAAc,CAAC;EAC5CC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,KAAK;MACdC,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE;IAChB,CAAC;EACH,CAAC;EACDC,QAAQ,EAAE;IACRC,OAAO,EAAE;MACPC,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAAChB,UAAU;MACxB,CAAC;MACDiB,GAAGA,CAACC,KAAK,EAAE;QACT,IAAI,CAACC,KAAK,CAAC,mBAAmB,EAAED,KAAK,CAAC;MACxC;IACF;EACF,CAAC;EACDE,KAAK,EAAE;IACLL,OAAOA,CAACM,MAAM,EAAE;MACd,IAAIA,MAAM,EAAE;QACV,IAAI,CAACC,YAAY,CAAC,CAAC;QACnB,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACzB,OAAO;QACL,IAAI,CAACC,eAAe,CAAC,CAAC;MACxB;IACF;EACF,CAAC;EACDC,OAAO,EAAE;IACP;IACAH,YAAYA,CAAA,EAAG;MACb,IAAI,CAAC,IAAI,CAAChB,SAAS,EAAE;MAErB,MAAMoB,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;MACvD,MAAMC,KAAI,GAAI;QACZC,KAAK,EAAE,IAAI,CAAC5B,WAAW;QACvB6B,GAAG,EAAEP,IAAI,CAACO;MACZ,CAAC;MAED,MAAMC,GAAE,GAAIrC,IAAG,GAAI,6CAA6C;MAChED,OAAO,CAACuC,IAAI,CAACD,GAAG,EAAEH,KAAK,CAAC,CAACK,IAAI,CAAEC,GAAG,IAAK;QACrC,IAAIA,GAAG,CAACC,OAAO,EAAE;UACf,IAAI,CAAC7B,QAAO,GAAI4B,GAAG,CAACC,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YACzC,OAAO,IAAIC,IAAI,CAACF,CAAC,CAACG,QAAQ,IAAI,IAAID,IAAI,CAACD,CAAC,CAACE,QAAQ,CAAC;UACpD,CAAC,CAAC;UACF,IAAI,CAACC,SAAS,CAAC,MAAM;YACnB,IAAI,CAACC,cAAc,CAAC,CAAC;UACvB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,CAACC,KAAK,CAAEC,KAAK,IAAK;QAClBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAACE,QAAQ,CAACF,KAAK,CAAC,UAAU,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC;IAED;IACAG,WAAWA,CAAA,EAAG;MACZ,IAAI,CAAC,IAAI,CAACxC,YAAY,CAACyC,IAAI,CAAC,CAAC,EAAE;QAC7B,IAAI,CAACF,QAAQ,CAACG,OAAO,CAAC,SAAS,CAAC;QAChC;MACF;MAEA,MAAM1B,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;MACvD,IAAI,CAACnB,OAAM,GAAI,IAAI;MAEnB,MAAMoB,KAAI,GAAI;QACZC,KAAK,EAAE,IAAI,CAAC5B,WAAW;QACvB6B,GAAG,EAAEP,IAAI,CAACO,GAAG;QACboB,OAAO,EAAE,IAAI,CAAC3C,YAAY;QAC1B4C,IAAI,EAAE,GAAG;QAAE;QACXX,QAAQ,EAAE,IAAID,IAAI,CAAC,CAAC,CAACa,cAAc,CAAC;MACtC,CAAC;MAED,MAAMrB,GAAE,GAAIrC,IAAG,GAAI,eAAe;MAClCD,OAAO,CAACuC,IAAI,CAACD,GAAG,EAAEH,KAAK,CAAC,CAACK,IAAI,CAAEC,GAAG,IAAK;QACrC,IAAI,CAAC1B,OAAM,GAAI,KAAK;QACpB,IAAI,CAACD,YAAW,GAAI,EAAE;QACtB,IAAI,CAACuC,QAAQ,CAACO,OAAO,CAAC,MAAM,CAAC;;QAE7B;QACAC,UAAU,CAAC,MAAM;UACf,IAAI,CAACnC,YAAY,CAAC,CAAC;QACrB,CAAC,EAAE,GAAG,CAAC;;QAEP;QACA,IAAI,CAACH,KAAK,CAAC,cAAc,CAAC;MAC5B,CAAC,CAAC,CAAC2B,KAAK,CAAEC,KAAK,IAAK;QAClB,IAAI,CAACpC,OAAM,GAAI,KAAK;QACpBqC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B,IAAI,CAACE,QAAQ,CAACF,KAAK,CAAC,QAAQ,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC;IAED;IACAF,cAAcA,CAAA,EAAG;MACf,MAAMa,SAAQ,GAAI,IAAI,CAACC,KAAK,CAACC,aAAa;MAC1C,IAAIF,SAAS,EAAE;QACbA,SAAS,CAACG,SAAQ,GAAIH,SAAS,CAACI,YAAY;MAC9C;IACF,CAAC;IAED;IACAvC,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACC,eAAe,CAAC,CAAC,EAAE;MACxB,IAAI,CAACZ,YAAW,GAAImD,WAAW,CAAC,MAAM;QACpC,IAAI,CAACzC,YAAY,CAAC,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC,EAAE;IACZ,CAAC;IAED;IACAE,eAAeA,CAAA,EAAG;MAChB,IAAI,IAAI,CAACZ,YAAY,EAAE;QACrBoD,aAAa,CAAC,IAAI,CAACpD,YAAY,CAAC;QAChC,IAAI,CAACA,YAAW,GAAI,IAAI;MAC1B;IACF,CAAC;IAED;IACAqD,UAAUA,CAACC,OAAO,EAAE;MAClB,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;MACvB,IAAI;QACF,MAAMC,IAAG,GAAI,IAAIzB,IAAI,CAACwB,OAAO,CAAC;QAC9B,MAAME,GAAE,GAAI,IAAI1B,IAAI,CAAC,CAAC;QACtB,MAAM2B,IAAG,GAAID,GAAE,GAAID,IAAI;;QAEvB;QACA,IAAIE,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,EAAC,GAAI,IAAG,IAAKF,IAAI,CAACG,OAAO,CAAC,MAAMF,GAAG,CAACE,OAAO,CAAC,CAAC,EAAE;UAClE,OAAOH,IAAI,CAACI,kBAAkB,CAAC,OAAO,EAAE;YACtCC,IAAI,EAAE,SAAS;YACfC,MAAM,EAAE;UACV,CAAC,CAAC;QACJ;;QAEA;QACA,MAAMC,SAAQ,GAAI,IAAIhC,IAAI,CAAC0B,GAAG,CAAC;QAC/BM,SAAS,CAACC,OAAO,CAACP,GAAG,CAACE,OAAO,CAAC,IAAI,CAAC,CAAC;QACpC,IAAIH,IAAI,CAACG,OAAO,CAAC,MAAMI,SAAS,CAACJ,OAAO,CAAC,CAAC,EAAE;UAC1C,OAAO,KAAI,GAAIH,IAAI,CAACI,kBAAkB,CAAC,OAAO,EAAE;YAC9CC,IAAI,EAAE,SAAS;YACfC,MAAM,EAAE;UACV,CAAC,CAAC;QACJ;;QAEA;QACA,OAAON,IAAI,CAACS,kBAAkB,CAAC,OAAO,IAAI,GAAE,GACrCT,IAAI,CAACI,kBAAkB,CAAC,OAAO,EAAE;UAC/BC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;QACV,CAAC,CAAC;MACX,EAAE,OAAOI,CAAC,EAAE;QACV,OAAOX,OAAO;MAChB;IACF,CAAC;IAED;IACAY,YAAYA,CAACC,OAAO,EAAE;MACpB,IAAIA,OAAO,CAACzB,IAAG,IAAK,GAAG,EAAE;QACvB;QACA,OAAOyB,OAAO,CAACC,GAAE,GAAI,IAAI,CAACnE,YAAW,GAAIkE,OAAO,CAACC,GAAG;MACtD,OAAO;QACL;QACA,OAAOD,OAAO,CAACE,GAAE,GAAI,IAAI,CAACpE,YAAW,GAAIkE,OAAO,CAACE,GAAE;MACrD;IACF,CAAC;IAID;IACAC,gBAAgBA,CAACC,KAAK,EAAE;MACtB,MAAMC,GAAE,GAAID,KAAK,CAACE,MAAM;MACxB,MAAMC,cAAa,GAAIF,GAAG,CAACG,OAAO,CAAC,eAAe,CAAC;MACnD,MAAMC,QAAO,GAAIF,cAAc,CAACG,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC;MACpEN,GAAG,CAACO,GAAE,GAAI,IAAI,CAACC,gBAAgB,CAACJ,QAAO,GAAI,QAAO,GAAI,SAAS,CAAC;IAClE,CAAC;IAED;IACAK,mBAAmBA,CAAA,EAAG;MACpB;MACA,MAAMC,cAAa,GAAI,IAAI,CAACrF,QAAQ,CAACsF,IAAI,CAACC,GAAE,IAAKA,GAAG,CAAC1C,IAAG,IAAK,GAAG,CAAC;MACjE,IAAIwC,cAAa,IAAKA,cAAc,CAACb,GAAG,EAAE;QACxC,OAAO,IAAI,CAACpE,YAAW,GAAIiF,cAAc,CAACb,GAAG;MAC/C;IACF,CAAC;IAED;IACAgB,sBAAsBA,CAACd,KAAK,EAAE;MAC5BA,KAAK,CAACE,MAAM,CAACM,GAAE,GAAI,IAAI,CAACC,gBAAgB,CAAC,SAAS,CAAC;IACrD,CAAC;IAED;IACAM,WAAWA,CAAA,EAAG;MACZ,IAAI,CAAC1E,eAAe,CAAC,CAAC;MACtB,IAAI,CAACT,OAAM,GAAI,KAAK;MACpB,IAAI,CAACL,YAAW,GAAI,EAAE;MACtB,IAAI,CAACD,QAAO,GAAI,EAAE;IACpB;EACF,CAAC;EACD0F,aAAaA,CAAA,EAAG;IACd,IAAI,CAAC3E,eAAe,CAAC,CAAC;EACxB;AACF,CAAC", "ignoreList": []}]}