{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Main.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Main.vue", "mtime": 1749361291843}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IEhlYWRlciBmcm9tICIuLi9jb21wb25lbnRzL0hlYWRlciI7CmltcG9ydCBMZWZ0TWVudSBmcm9tICIuLi9jb21wb25lbnRzL0xlZnRNZW51IjsKaW1wb3J0IHsgRWxDb25maWdQcm92aWRlciB9IGZyb20gImVsZW1lbnQtcGx1cyI7CmltcG9ydCB6aENuIGZyb20gImVsZW1lbnQtcGx1cy9saWIvbG9jYWxlL2xhbmcvemgtY24iOwppbXBvcnQgJCBmcm9tICdqcXVlcnknOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIk1haW5MYXlvdXQiLAogIGNvbXBvbmVudHM6IHsKICAgIEhlYWRlciwKICAgIExlZnRNZW51LAogICAgW0VsQ29uZmlnUHJvdmlkZXIubmFtZV06IEVsQ29uZmlnUHJvdmlkZXIKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsb2NhbGU6IHpoQ24KICAgIH07CiAgfSwKICBtb3VudGVkKCkge30sCiAgbWV0aG9kczoge30KfTs="}, {"version": 3, "names": ["Header", "LeftMenu", "ElConfigProvider", "zhCn", "$", "name", "components", "data", "locale", "mounted", "methods"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Main.vue"], "sourcesContent": ["<template>\r\n\r\n<el-config-provider :locale=\"locale\">\r\n\r\n  <div class=\"main-wrapper\" id=\"main-wrapper\">\r\n\r\n\r\n  <Header />\r\n  <LeftMenu />\r\n\r\n  <div class=\"content-body\">\r\n        <!-- row -->\t\r\n  <div class=\"page-titles\">\r\n    <ol class=\"breadcrumb\">\r\n      <li><h5 class=\"bc-title\" id=\"title1\">{{ this.$route.meta.title }}</h5></li>\r\n      <li class=\"breadcrumb-item\"><a href=\"/main\">\r\n        <svg width=\"17\" height=\"17\" viewBox=\"0 0 17 17\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n          <path d=\"M2.125 6.375L8.5 1.41667L14.875 6.375V14.1667C14.875 14.5424 14.7257 14.9027 14.4601 15.1684C14.1944 15.4341 13.8341 15.5833 13.4583 15.5833H3.54167C3.16594 15.5833 2.80561 15.4341 2.53993 15.1684C2.27426 14.9027 2.125 14.5424 2.125 14.1667V6.375Z\" stroke=\"#2C2C2C\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n          <path d=\"M6.375 15.5833V8.5H10.625V15.5833\" stroke=\"#2C2C2C\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        </svg>\r\n        首页 </a>\r\n      </li>\r\n      <li class=\"breadcrumb-item active\"><a href=\"javascript:void(0)\" id=\"title2\">{{ this.$route.meta.title }}</a></li>\r\n    </ol>\r\n    \r\n  </div>\r\n  <div class=\"container-fluid\" style=\"\">\r\n    <router-view />    \r\n  </div>\r\n </div>\r\n\r\n\r\n\r\n</div>\r\n</el-config-provider>\r\n</template>\r\n\r\n<script>\r\nimport Header from \"../components/Header\";\r\nimport LeftMenu from \"../components/LeftMenu\";\r\nimport { ElConfigProvider } from \"element-plus\";\r\nimport zhCn from \"element-plus/lib/locale/lang/zh-cn\";\r\nimport $ from 'jquery';\r\nexport default {\r\n  name: \"MainLayout\",\r\n  components: {\r\n    Header,\r\n    LeftMenu,\r\n    [ElConfigProvider.name]: ElConfigProvider,\r\n  },\r\n  data() {\r\n    return {\r\n      locale: zhCn,\r\n    };\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n\r\n  methods: {\r\n\r\n  },\r\n};\r\n</script>\r\n\r\n\r\n<style scoped>\r\n@import url(../assets/css/h_style.css);\r\n</style>\r\n\r\n"], "mappings": "AAsCA,OAAOA,MAAK,MAAO,sBAAsB;AACzC,OAAOC,QAAO,MAAO,wBAAwB;AAC7C,SAASC,gBAAe,QAAS,cAAc;AAC/C,OAAOC,IAAG,MAAO,oCAAoC;AACrD,OAAOC,CAAA,MAAO,QAAQ;AACtB,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,UAAU,EAAE;IACVN,MAAM;IACNC,QAAQ;IACR,CAACC,gBAAgB,CAACG,IAAI,GAAGH;EAC3B,CAAC;EACDK,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,MAAM,EAAEL;IACV,CAAC;EACH,CAAC;EACDM,OAAOA,CAAA,EAAG,CAEV,CAAC;EAEDC,OAAO,EAAE,CAET;AACF,CAAC", "ignoreList": []}]}