{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\doctor\\DoctorScheduleCalendar.vue?vue&type=template&id=e29a7b4c&scoped=true", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\doctor\\DoctorScheduleCalendar.vue", "mtime": 1749360634991}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\doctor\\DoctorScheduleCalendar.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE1C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACf,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC5E;YACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC3D,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACvD,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/admin/doctor/DoctorScheduleCalendar.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"schedule-calendar\">\n    <div class=\"header\">\n \n      <div class=\"doctor-info\" v-if=\"doctorInfo\">\n        <span>医生：{{ doctorInfo.dname }}</span>\n        <span>科室：{{ doctorInfo.pname }}</span>\n      </div>\n    </div>\n\n    <div class=\"calendar-container\" v-loading=\"loading\">\n      <!-- 日期表头 -->\n      <div class=\"schedule-header\">\n        <div class=\"time-column\">时间段</div>\n        <div class=\"date-column\" v-for=\"(date, index) in dates\" :key=\"index\">\n          <div class=\"date\">{{ date }}</div>\n          <div class=\"week-day\">{{ weeks[index] }}</div>\n        </div>\n      </div>\n\n      <!-- 排班表格 -->\n      <div class=\"schedule-body\">\n        <div class=\"schedule-row\" v-for=\"(timeSlot, timeIndex) in timeSlots\" :key=\"timeIndex\">\n          <!-- 时间段 -->\n          <div class=\"time-cell\">{{ timeSlot }}</div>\n\n          <!-- 每天的排班情况 -->\n          <div \n            class=\"schedule-cell\" \n            v-for=\"(weekDay, dayIndex) in weeks\" \n            :key=\"dayIndex\"\n            :class=\"{ 'has-schedule': getScheduleInfo(timeSlot, weekDay).hasSchedule }\"\n          >\n            <div v-if=\"getScheduleInfo(timeSlot, weekDay).hasSchedule\" class=\"schedule-info\">\n              <div class=\"status available\">可预约</div>\n              <div class=\"remaining\">\n                剩余：{{ getScheduleInfo(timeSlot, weekDay).availableSlots }}\n              </div>\n              <div class=\"total\">\n                总数：{{ getScheduleInfo(timeSlot, weekDay).totalSlots }}\n              </div>\n            </div>\n            <div v-else class=\"no-schedule\">\n              <div class=\"status unavailable\">未排班</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 统计信息 -->\n    <div class=\"statistics\">\n      <el-card class=\"stat-card\">\n        <div class=\"stat-item\">\n          <span class=\"stat-label\">本周排班天数：</span>\n          <span class=\"stat-value\">{{ weeklyStats.scheduledDays }}</span>\n        </div>\n        <div class=\"stat-item\">\n          <span class=\"stat-label\">本周总号数：</span>\n          <span class=\"stat-value\">{{ weeklyStats.totalSlots }}</span>\n        </div>\n        <div class=\"stat-item\">\n          <span class=\"stat-label\">本周已预约：</span>\n          <span class=\"stat-value\">{{ weeklyStats.bookedSlots }}</span>\n        </div>\n        <div class=\"stat-item\">\n          <span class=\"stat-label\">本周剩余：</span>\n          <span class=\"stat-value\">{{ weeklyStats.availableSlots }}</span>\n        </div>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\n\nexport default {\n  name: 'DoctorScheduleCalendar',\n  data() {\n    return {\n      loading: false,\n      doctorInfo: null,\n      dates: [],\n      weeks: [],\n      timeSlots: [\n        \"8:00-9:00\",\n        \"9:00-10:00\", \n        \"10:00-11:00\",\n        \"11:00-12:00\",\n        \"14:00-15:00\",\n        \"15:00-16:00\",\n        \"16:00-17:00\"\n      ],\n      scheduleData: [],\n      weeklyStats: {\n        scheduledDays: 0,\n        totalSlots: 0,\n        bookedSlots: 0,\n        availableSlots: 0\n      }\n    };\n  },\n  created() {\n    this.initDates();\n    this.getDoctorInfo();\n    this.getScheduleData();\n  },\n  methods: {\n    // 初始化未来7天的日期\n    initDates() {\n      const today = new Date();\n      const weekMap = {\n        0: \"星期日\",\n        1: \"星期一\", \n        2: \"星期二\",\n        3: \"星期三\",\n        4: \"星期四\",\n        5: \"星期五\",\n        6: \"星期六\"\n      };\n\n      for (let i = 0; i < 7; i++) {\n        const date = new Date(today);\n        date.setDate(today.getDate() + i);\n        \n        const month = (date.getMonth() + 1).toString().padStart(2, '0');\n        const day = date.getDate().toString().padStart(2, '0');\n        const dateStr = `${month}-${day}`;\n        \n        this.dates.push(dateStr);\n        this.weeks.push(weekMap[date.getDay()]);\n      }\n    },\n\n    // 获取医生信息\n    getDoctorInfo() {\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      if (!user || !user.did) {\n        this.$message.error('请先登录');\n        return;\n      }\n\n      this.loading = true;\n      const url = base + \"/doctor/get?id=\" + user.did;\n      request.post(url).then((res) => {\n        this.doctorInfo = res.resdata;\n        this.loading = false;\n      }).catch((error) => {\n        console.error('获取医生信息失败:', error);\n        this.loading = false;\n        this.$message.error('获取医生信息失败');\n      });\n    },\n\n    // 获取排班数据\n    getScheduleData() {\n      const user = JSON.parse(sessionStorage.getItem(\"user\"));\n      if (!user || !user.did) {\n        return;\n      }\n\n      this.loading = true;\n      const param = {\n        f: 5,\n        did: user.did,\n        loadmsg: '正在加载中'\n      };\n\n      const url = base + \"/plans/list3?currentPage=1&pageSize=500\";\n      request.post(url, param).then((res) => {\n        if (res.resdata && res.resdata.length > 0) {\n          this.scheduleData = res.resdata;\n          this.calculateWeeklyStats();\n        }\n        this.loading = false;\n      }).catch((error) => {\n        console.error('获取排班数据失败:', error);\n        this.loading = false;\n        this.$message.error('获取排班数据失败');\n      });\n    },\n\n    // 获取指定时间段和星期的排班信息\n    getScheduleInfo(timeSlot, weekDay) {\n      const schedule = this.scheduleData.find(\n        item => item.ptime === timeSlot && item.weeks === weekDay\n      );\n\n      if (schedule) {\n        const totalSlots = parseInt(schedule.people) || 0;\n        const bookedSlots = parseInt(schedule.by1) || 0;\n        const availableSlots = totalSlots - bookedSlots;\n\n        return {\n          hasSchedule: true,\n          totalSlots: totalSlots,\n          bookedSlots: bookedSlots,\n          availableSlots: availableSlots > 0 ? availableSlots : 0\n        };\n      }\n\n      return {\n        hasSchedule: false,\n        totalSlots: 0,\n        bookedSlots: 0,\n        availableSlots: 0\n      };\n    },\n\n    // 计算本周统计数据\n    calculateWeeklyStats() {\n      let scheduledDays = new Set();\n      let totalSlots = 0;\n      let bookedSlots = 0;\n\n      this.scheduleData.forEach(schedule => {\n        if (this.weeks.includes(schedule.weeks)) {\n          scheduledDays.add(schedule.weeks);\n          totalSlots += parseInt(schedule.people) || 0;\n          bookedSlots += parseInt(schedule.by1) || 0;\n        }\n      });\n\n      this.weeklyStats = {\n        scheduledDays: scheduledDays.size,\n        totalSlots: totalSlots,\n        bookedSlots: bookedSlots,\n        availableSlots: totalSlots - bookedSlots\n      };\n    }\n  }\n};\n</script>\n\n<style scoped>\n.schedule-calendar {\n  padding: 20px;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header h2 {\n  margin: 0 0 10px 0;\n  color: #333;\n  font-size: 24px;\n}\n\n.doctor-info {\n  color: #666;\n  font-size: 14px;\n}\n\n.doctor-info span {\n  margin-right: 20px;\n}\n\n.calendar-container {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.schedule-header {\n  display: flex;\n  background-color: #f8f9fa;\n  border-bottom: 2px solid #dee2e6;\n}\n\n.time-column {\n  width: 120px;\n  padding: 15px 10px;\n  text-align: center;\n  font-weight: bold;\n  background-color: #e9ecef;\n  border-right: 1px solid #dee2e6;\n}\n\n.date-column {\n  flex: 1;\n  padding: 10px;\n  text-align: center;\n  border-right: 1px solid #dee2e6;\n  min-width: 100px;\n}\n\n.date {\n  font-weight: bold;\n  font-size: 16px;\n  color: #333;\n}\n\n.week-day {\n  font-size: 12px;\n  color: #666;\n  margin-top: 4px;\n}\n\n.schedule-body {\n  border-bottom: 1px solid #dee2e6;\n}\n\n.schedule-row {\n  display: flex;\n  border-bottom: 1px solid #dee2e6;\n}\n\n.schedule-row:last-child {\n  border-bottom: none;\n}\n\n.time-cell {\n  width: 120px;\n  padding: 20px 10px;\n  text-align: center;\n  background-color: #f8f9fa;\n  border-right: 1px solid #dee2e6;\n  font-weight: 500;\n  color: #495057;\n}\n\n.schedule-cell {\n  flex: 1;\n  padding: 15px 10px;\n  text-align: center;\n  border-right: 1px solid #dee2e6;\n  min-height: 80px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 100px;\n}\n\n.schedule-cell.has-schedule {\n  background-color: #e8f5e8;\n}\n\n.schedule-info {\n  width: 100%;\n}\n\n.status.available {\n  color: #28a745;\n  font-weight: bold;\n  margin-bottom: 5px;\n}\n\n.status.unavailable {\n  color: #6c757d;\n  font-style: italic;\n}\n\n.remaining, .total {\n  font-size: 12px;\n  color: #666;\n  margin: 2px 0;\n}\n\n.no-schedule {\n  width: 100%;\n  color: #999;\n}\n\n.statistics {\n  margin-top: 20px;\n}\n\n.stat-card {\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.stat-item {\n  display: inline-block;\n  margin-right: 30px;\n  margin-bottom: 10px;\n}\n\n.stat-label {\n  color: #666;\n  font-size: 14px;\n}\n\n.stat-value {\n  color: #333;\n  font-weight: bold;\n  font-size: 16px;\n  margin-left: 5px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .schedule-header,\n  .schedule-row {\n    min-width: 800px;\n  }\n  \n  .calendar-container {\n    overflow-x: auto;\n  }\n  \n  .stat-item {\n    display: block;\n    margin-bottom: 15px;\n  }\n}\n</style>\n"]}]}