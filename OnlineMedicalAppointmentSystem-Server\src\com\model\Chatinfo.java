package com.model;
import java.util.List;

/**
* (chatinfo)聊天实体类
*/
public class Chatinfo extends ComData{

	private static final long serialVersionUID = 462487861816667L;
	private Integer cid;    //聊天id
	private String lname;    //用户名
	private Integer did;    //医生id
	private String content;    //聊天内容
	private String sendtime;    //发送时间
	private Integer flag;    //标识
	private String by1;    //用户头像
	private String by2;    //医生头像

	public Integer getCid() {
		return cid;
	}

	public void setCid(Integer cid) {
		this.cid = cid;
	}

	public String getLname() {
		return lname;
	}

	public void setLname(String lname) {
		this.lname = lname;
	}

	public Integer getDid() {
		return did;
	}

	public void setDid(Integer did) {
		this.did = did;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getSendtime() {
		return sendtime;
	}

	public void setSendtime(String sendtime) {
		this.sendtime = sendtime;
	}

	public Integer getFlag() {
		return flag;
	}

	public void setFlag(Integer flag) {
		this.flag = flag;
	}

	public String getBy1() {
		return by1;
	}

	public void setBy1(String by1) {
		this.by1 = by1;
	}

	public String getBy2() {
		return by2;
	}

	public void setBy2(String by2) {
		this.by2 = by2;
	}

}

